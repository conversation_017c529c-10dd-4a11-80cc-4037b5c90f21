#include "CppBase.h"
#include "RMAgent.h"
#include "tombstone_client.h"

#include "DdsParserMnger.h"
#include "RecentDataCache.h"

#include "mlog/Logging.h"

static std::atomic<bool> _exitall(false);
static void _CustomSignalCallback(int32_t sig) {

    MLOG_E("Custom signal callback called for signal: {}", sig);

    _exitall = false;
}

int32_t main(int32_t argc, const char *argv[]) {

    minieye::tombstone::debuggerdInit(_CustomSignalCallback);

    minieye::mlog::LogConfig mlogConf;
    mlogConf.tag = "takepoint_test";
    mlogConf.level = minieye::mlog::LogDebug;
    mlogConf.isRemoteLog  = true;
    mlogConf.isLogFile  = false;
    mlogConf.logDomain = minieye::mlog::DomainApp;
    MLOG_INIT(mlogConf);

    minieye::collect::RecentDataCache::GetInstance()->Init();
    minieye::collect::DdsParserMnger::GetInstance()->Init();

    for ( ; !_exitall; ) {
        minieye::system::Thread::msleep(100);
    }

    return 0;
}
