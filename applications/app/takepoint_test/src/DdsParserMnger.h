#pragma once

#include "CppBase.h"

#include "dds/dds_api.h"
#include "dds/dds_capi.h"
#include "dds/dds_common.h"

namespace minieye {
namespace collect {

class DdsParserMnger {

 public:

    bool Init();
    bool UnInit();

    static DdsParserMnger *GetInstance();

 private:

    DdsParserMnger();
    ~DdsParserMnger();

 private:

    std::shared_ptr<minieye::DDS::Context>                      mCtx_;
    std::mutex                                                  mParserListMtx_;
    std::vector<std::string>                                    mValidTopicList_;

    std::vector<std::shared_ptr<minieye::DDS::Reader>>          mReaderList_;
};

}
}
