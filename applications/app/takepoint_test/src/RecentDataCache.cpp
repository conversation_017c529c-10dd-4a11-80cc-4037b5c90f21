#include "RecentDataCache.h"

#include "mlog/Logging.h"
#include <inttypes.h>

namespace minieye {
namespace collect {

RecentDataCache::RecentDataCache() {

}

RecentDataCache::~RecentDataCache() {

}

//----------------------------------------------------------------

bool RecentDataCache::Init() {

    mThread_ = std::make_unique<minieye::system::Thread>([this]() -> void {
        Run_();
    });
    mThread_->Start("RecentDataCache");

    return true;
}

bool RecentDataCache::UnInit() {

    MLOG_D("Waiting thread stop ...");

    if (mThread_ != nullptr) {
        mThread_->Stop();
        mThread_->Stop(1000);
    }

    MLOG_D("Thread exited .");

    return true;
}

//----------------------------------------------------------------

bool RecentDataCache::SetDid(const std::string& did) {

    if (did == "") {
        return false;
    }

    std::lock_guard<std::mutex> lock(mMtx_);
    auto iter = mDataRecordPtrQMap_.find(did);
    if (iter == mDataRecordPtrQMap_.end()) {
        // MLOG_D("did = {}", did);
        mDataRecordPtrQMap_[did] = ADataRecordPtrQ();
    }

    return true;
}

//----------------------------------------------------------------

ADataRecordPtr_t RecentDataCache::NewADataRecordPtr(const std::string& did) {

    ADataRecordPtr_t aDataRecordPtr = ADataRecordPtr(nullptr);

    if (did == "") {
        return aDataRecordPtr;
    }

    aDataRecordPtr = std::make_shared<ADataRecord_t>();
    if (aDataRecordPtr.get() == nullptr) {
        MLOG_C("The did {} new ADataRecord error !", did);
        return aDataRecordPtr;
    }
    aDataRecordPtr->did = did;
    aDataRecordPtr->recordingtms = minieye::system::GetTimestampMs();
    aDataRecordPtr->recordingclkms = minieye::system::GetClkMs();

    return aDataRecordPtr;
}

bool RecentDataCache::OnData(ADataRecordPtr_t& aDataRecordPtr) {

    std::string did;

    if (aDataRecordPtr.get() == nullptr ||
        aDataRecordPtr->did == "" ||
        aDataRecordPtr->buffer.size() <= 0
    ) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mMtx_);

    did = aDataRecordPtr->did;

    auto iter = mDataRecordPtrQMap_.find(did);
    if (iter == mDataRecordPtrQMap_.end()) {
        MLOG_E_EVERY_TIME(2000, "The did {} not seted !", did);
        return false;
    }
    ADataRecordPtrQ_t& aDataRecordPtrQ = iter->second;

    aDataRecordPtr->recordingtms = minieye::system::GetTimestampMs();
    aDataRecordPtr->recordingclkms = minieye::system::GetClkMs();

    aDataRecordPtrQ.emplace_back(aDataRecordPtr);

    return true;
}

//--------------------------------------------------------------------------

inline bool RecentDataCache::Cleanup_() {

    std::lock_guard<std::mutex> lock(mMtx_);

    for (auto iter = mDataRecordPtrQMap_.begin(); iter != mDataRecordPtrQMap_.end(); iter++) {

        const std::string& topic = iter->first;
        ADataRecordPtrQ_t& aDataRecordPtrQ = iter->second;

        for ( ; aDataRecordPtrQ.size() > 0; ) {
            // MLOG_D(">>> Cleanup topic: {}", topic);
            aDataRecordPtrQ.pop_front();
        }

        MLOG_D(">>> Topic: {}, aDataRecordPtrQ size: {} ",
            topic, aDataRecordPtrQ.size());
    }

    return true;
}

inline void RecentDataCache::Run_() {

    uint64_t lastclkms = 0;

    for ( ; !mThread_->IsExiting(); ) {

        if (minieye::system::GetClkMs() - lastclkms >= 30000) {
            lastclkms = minieye::system::GetClkMs();

            MLOG_D("It's time to cleanup ...");
            Cleanup_();
        }

        minieye::system::Thread::msleep(100);
    }
}

RecentDataCache *RecentDataCache::GetInstance() {

    static std::mutex mtx;
    static RecentDataCache *instance = nullptr;

    if (instance == nullptr) {
        std::lock_guard<std::mutex> lock(mtx);
        if (instance == nullptr) {
            instance = new RecentDataCache();
        }
    }

    return instance;
}

}
}