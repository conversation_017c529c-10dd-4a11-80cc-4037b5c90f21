#pragma once

#include "CppBase.h"

#include "mlog/Logging.h"

namespace minieye {
namespace collect {

template<typename T>
class SafeQueue {

 public:

    // 禁止拷贝构造和赋值
    SafeQueue(const SafeQueue&) = delete;
    SafeQueue& operator=(const SafeQueue&) = delete;

    explicit SafeQueue(int32_t depth = -1)
        : mDepth_(depth) {

    }

    ~SafeQueue() {

    }

    void SetDepth(int32_t depth) {
        std::lock_guard<std::mutex> lock(mMtx_);
        mDepth_ = depth;
    }

    bool Sort(std::function<bool(const T& a, const T& b)> CompareHdler) {
        if (CompareHdler == nullptr) {
            return false;
        }
        std::lock_guard<std::mutex> lock(mMtx_);
        std::sort(mQ_.begin(), mQ_.end(), CompareHdler);
        return true;
    }

    bool Clear() {
        std::lock_guard<std::mutex> lock(mMtx_);
        mQ_.clear();
        return true;
    }

    bool PutQ(const T& t) {
        std::lock_guard<std::mutex> lock(mMtx_);
        for ( ; mDepth_ > 0 && static_cast<int32_t>(mQ_.size()) >= mDepth_; ) {
            mQ_.pop_front();
        }
        mQ_.emplace_back(t);
        return true;
    }

    bool PopQ(T& t) {
        std::lock_guard<std::mutex> lock(mMtx_);
        if (mQ_.size() <= 0) {
            return false;
        }
        t = mQ_.front();
        mQ_.pop_front();
        return true;
    }

    bool FrontQ(T& t) {
        std::lock_guard<std::mutex> lock(mMtx_);
        if (mQ_.size() <= 0) {
            return false;
        }
        t = mQ_.front();
        return true;
    }

    bool PopFrontQ() {
        std::lock_guard<std::mutex> lock(mMtx_);
        if (mQ_.size() <= 0) {
            return true;
        }
        mQ_.pop_front();
        return true;
    }

    int32_t Size() {
        std::lock_guard<std::mutex> lock(mMtx_);
        return mQ_.size();
    }

    typedef enum ForeachRet : uint8_t {

        E_FOREACH_RET_CONTINUE = 0, // 继续
        E_FOREACH_RET_BREAK,        // 结束
        E_FOREACH_RET_ERASE_CONTINUE, // 删掉当前元素并继续
        E_FOREACH_RET_ERASE_BREAK, // 删掉当前元素并结束

    } ForeachRet_t;
    bool Foreach(std::function<ForeachRet_t(T& t)> hdler) {

        if (hdler == nullptr) {
            return false;
        }

        std::lock_guard<std::mutex> lock(mMtx_);
        for (auto iter = mQ_.begin(); iter != mQ_.end(); ) {
            ForeachRet_t ret = hdler(*iter);
            if (ret == E_FOREACH_RET_CONTINUE) {
                iter++;
            } else if (ret == E_FOREACH_RET_BREAK) {
                break;
            } else if (ret == E_FOREACH_RET_ERASE_CONTINUE) {
                iter = mQ_.erase(iter);
            } else if (ret == E_FOREACH_RET_ERASE_BREAK) {
                iter = mQ_.erase(iter);
                break;
            } else {
                MLOG_E("Wtf ?! Unknown return value: {} !", ret);
                return false;
            }
        }

        return true;
    }

 private:

    std::mutex                  mMtx_;
    std::deque<T>               mQ_;

    std::atomic<int32_t>        mDepth_;
};

}
}
