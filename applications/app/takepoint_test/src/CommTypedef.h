#pragma once

#include <unordered_map>

#include "CppBase.h"

namespace minieye {
namespace collect {

/**
 * 一种did数据记录结构体
 */
typedef struct ADataRecord {

    ADataRecord() { }
    ~ADataRecord() { }

    std::string                                     did;                    // 数据类型ID
    uint64_t                                        recordingtms = 0;       // 数据记录时的时间戳
    uint64_t                                        recordingclkms = 0;     // 数据记录时的相对时间

    std::vector<char>                               buffer;                 // 数据报文

} ADataRecord_t;

typedef std::shared_ptr<ADataRecord_t>              ADataRecordPtr, ADataRecordPtr_t;
typedef std::deque<ADataRecordPtr_t>                ADataRecordPtrQ, ADataRecordPtrQ_t;

}
}
