/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : x<PERSON><PERSON><PERSON>n
 * Date         : 2025-08-11
 * Description  : 文件描述
 */
#include "DdsParserMnger.h"

#include "RecentDataCache.h"
#include "JsonUtil.h"

#include "mlog/Logging.h"

namespace minieye {
namespace collect {

DdsParserMnger::DdsParserMnger() {

}

DdsParserMnger::~DdsParserMnger() {

}

bool DdsParserMnger::Init() {

    minieye::json::JsonObject_t jsonObject = minieye::json::INVALID_JSON_OBJECT;

    mCtx_ = std::make_shared<minieye::DDS::Context>("/app/etc/dds/topic.json", true);

    jsonObject = minieye::json::FileToJsonObject("/app/etc/dds/topic.json");
    minieye::json::JsonObject_t arrayObj = minieye::json::GetJsonObjectValue(jsonObject, "topics");
    minieye::json::JsonArrayObjectForeach(arrayObj, [&](minieye::json::JsonObject_t elemObject) -> bool {

        std::string topic = minieye::json::GetStringValue(elemObject, "topic", "");
        if (topic != "" &&
            std::find(mValidTopicList_.begin(),
                mValidTopicList_.end(), topic) == mValidTopicList_.end()
        ) {
            mValidTopicList_.emplace_back(topic);

            RecentDataCache::GetInstance()->SetDid(topic);
            std::shared_ptr<minieye::DDS::Reader> reader = std::make_shared<minieye::DDS::Reader>(
                mCtx_.get(),
                topic,
                [](const char *topic_, size_t index, void *ptr, size_t size, void *user) {
                    // MLOG_D("====== topic: {} index: {} size: {}", topic_, index, size);

                    ADataRecordPtr_t aDataRecordPtr = RecentDataCache::GetInstance()->NewADataRecordPtr(topic_);
                    if (aDataRecordPtr.get() == nullptr) {
                        MLOG_C("RecentDataCache::GetInstance()->NewADataRecordPtr() error for topic: {} !", topic_);
                    } else {
                        const char *data = reinterpret_cast<const char *>(ptr);
                        aDataRecordPtr->buffer.insert(aDataRecordPtr->buffer.end(), data, data + size);
                        RecentDataCache::GetInstance()->OnData(aDataRecordPtr);
                    }
                },
                this
            );
            mReaderList_.emplace_back(reader);

            MLOG_D("====> {}", topic);
        }
        return true;
    });

    return true;
}

bool DdsParserMnger::UnInit() {
    return true;
}

DdsParserMnger *DdsParserMnger::GetInstance() {

    static std::mutex mtx;
    static DdsParserMnger *instance = nullptr;

    if (instance == nullptr) {
        std::lock_guard<std::mutex> lock(mtx);
        if (instance == nullptr) {
            instance = new DdsParserMnger();
        }
    }

    return instance;
}

}
}
