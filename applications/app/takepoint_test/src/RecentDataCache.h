#pragma once

#include "dump_interface/DumpManager.h"
#include "dump_interface/IDumpSys.h"

#include "CppBase.h"
#include "CommTypedef.h"
#include "SafeQueue.h"

namespace minieye {
namespace collect {

class RecentDataCache {

 public:

    bool Init();
    bool UnInit();

    /**
     * @brief 设置DID
     * 只有设置了的DID数据才会被缓存
     * 
     * @param did 
     * @return true 
     * @return false 
     */
    bool SetDid(const std::string& did);

    /**
     * @brief 喂数据到缓存
     * 前提是did必须设置
     * @param did 
     * @return ADataRecordPtr_t 
     */
    ADataRecordPtr_t NewADataRecordPtr(const std::string& did);
    bool OnData(ADataRecordPtr_t& aDataRecordPtr);

    static RecentDataCache *GetInstance();

 private:

    RecentDataCache();
    ~RecentDataCache();

    inline bool IsMemPoolAvailable_();

    inline bool Cleanup_();

    inline void Run_();

 private:

    std::mutex                                      mMtx_;

    std::unordered_map<std::string, ADataRecordPtrQ_t>  mDataRecordPtrQMap_;

    std::unique_ptr<minieye::system::Thread>        mThread_;
};

}
}
