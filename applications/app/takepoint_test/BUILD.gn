import("//build/minieye/minieye.gni")

minieye_group("takepoint_test_group") {
    deps = [
        ":takepoint_test",
        ":takepoint_test_sh",
    ]
}

minieye_executable("takepoint_test") {

    include_dirs = [
        "//platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/usr/minieye/include",
        "//platform/D4Q/build/minieye_sdk/include/camera",
        "//middleware/dumpsys/include/",
        "src",
    ]

    sources = [

        "src/DdsParserMnger.cpp",
        "src/RecentDataCache.cpp",
        "src/main.cpp",
    ]

    ldflags = [
        "-ldds_api",
        "-ldds",
        "-lprotobuf",
        "-lz",
    ]

    public_deps = [

    ]

    relative_install_dir = "base/takepoint_test/bin"

    deps = [
        "//middleware/system/core/libcppbase:cppbase",
        "//middleware/mlog:libmlog",
        "//middleware/dumpsys:dumpsys_group",
        "//middleware/system/core/libjsonUtil:jsonUtil",
        "//middleware/communication/libevutil:libevutil",
        "//middleware/tombstone/src/debuggerd:tombstone_client",
        "//applications/app/common/libRMAgent:libRMAgent",
        "//applications/app/common/pb:data_proto_o",
        "//applications/app/state_manager:StateAgent",
    ]
}

minieye_prebuilt_etc("takepoint_test_sh") {
    source = "run.sh"
    relative_install_dir = "base/takepoint_test/"
}
