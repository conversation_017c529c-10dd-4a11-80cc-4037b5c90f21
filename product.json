{"platform": "D4Q", "build_type": "debug", "subsystem": [{"name": "applications", "component": [{"name": "phm_group", "dir": "//applications/app/phm:phm_group", "features": []}, {"name": "sr_hmi_client_group", "dir": "//applications/app/sr_hmi_client:sr_hmi_client_group", "features": []}, {"name": "idvr_group", "dir": "//applications/app/idvr:idvr_group", "features": []}, {"name": "test_group", "dir": "//applications/app/test:test_group", "features": []}, {"name": "upgrade_group", "dir": "//applications/bsp_app/ota:upgrade_group", "features": []}, {"name": "sr_service_group", "dir": "//applications/app/sr_hmi_service:sr_service_group", "features": []}, {"name": "vsomeip_group", "dir": "//middleware/someip:vsomeip3_group", "features": []}, {"name": "ets_service", "dir": "//middleware/ets_service:ets_service_group", "features": []}, {"name": "upper_tester", "dir": "//middleware/upper_tester:upper_tester_group", "features": []}, {"name": "state_group", "dir": "//applications/app/state_manager:state_manager_group", "features": []}, {"name": "canout_group", "dir": "//applications/app/canout:canout_group", "features": []}, {"name": "caninput_group", "dir": "//applications/app/caninput:caninput_group", "features": []}, {"name": "qxids_sdk_group", "dir": "//applications/app/qxids_sdk:qxids_sdk_group", "features": []}, {"name": "imu_group", "dir": "//applications/app/imu:imu_group", "features": []}, {"name": "libdecode_group", "dir": "//applications/bsp_app/libdecode:libdecode_group", "features": []}, {"name": "app_avm_out", "dir": "//applications/bsp_app/app_avm_out:app_avm_out_group", "features": []}, {"name": "encode_libflow", "dir": "//applications/bsp_app/encode_libflow:camera_encode_libflow_group", "features": []}, {"name": "camera_service", "dir": "//applications/bsp_app/camera_service:camera_service_group", "features": []}, {"name": "avm_pym_stitch", "dir": "//applications/bsp_app/avm_pym_stitch:avm_pym_stitch_group", "features": []}, {"name": "timesync_group", "dir": "//applications/bsp_app/timesync:timesync_group", "features": []}, {"name": "sync_status", "dir": "//applications/bsp_app/sync_status:sync_status", "features": []}, {"name": "can_utils_group", "dir": "//applications/bsp_app/can_utils:can_utils_group", "features": []}, {"name": "eth_diagnosis_group", "dir": "//applications/bsp_app/eth_diagnosis:eth_diagnosis_group", "features": []}, {"name": "library_common", "dir": "//applications/app/common:common_group", "features": []}, {"name": "gnss", "dir": "//applications/app/gnss:gnss_group", "features": []}, {"name": "timehal_sample", "dir": "//applications/app/common/timehal/:libminieyetimehal_group", "features": []}, {"name": "library_nanomsg", "dir": "//middleware/communication/nanomsg:library_nanomsg_group", "features": []}, {"name": "dumpsys", "dir": "//middleware/dumpsys:dumpsys_group", "features": []}, {"name": "filelog_demo", "dir": "//middleware/system/core/filelog/demo/:filelog_demo", "features": []}, {"name": "scantree", "dir": "//middleware/system/tools/scantree:scantree", "features": []}, {"name": "file_monitor", "dir": "//middleware/system/tools/filemonitor:file_monitor", "features": []}, {"name": "sample_message", "dir": "//middleware/system/core/libmessage/demo:sample_message", "features": []}, {"name": "daemon", "dir": "//middleware/daemon/:daemon_group", "features": []}, {"name": "mlog_sample", "dir": "//middleware/mlog/sample/:mlog_sample", "features": []}, {"name": "tombstone", "dir": "//middleware/tombstone/:tombstone_group", "features": []}, {"name": "logd", "dir": "//middleware/logd/:logd_group", "features": []}, {"name": "system_res_monitor", "dir": "//middleware/system/tools/system_res_monitor/:system_res_monitor", "features": []}, {"name": "flex_diagnosis", "dir": "//applications/bsp_app/flex_diagnosis:flex_diagnosis_group", "features": []}, {"name": "flexidag_factory", "dir": "//applications/bsp_app/flexidag_factory:flexidag_factory_group", "features": []}, {"name": "sys_perf_daemon_group", "dir": "//applications/bsp_app/sys_perf_daemon:sys_perf_daemon", "features": []}, {"name": "diagnosis_group", "dir": "//applications/app/diagnosis:diagnosis_group", "features": []}, {"name": "doip", "dir": "//applications/app/doip:doip_group", "features": []}, {"name": "radar_group", "dir": "//applications/app/radar/:radar_group", "features": []}, {"name": "sr_client_simulation", "dir": "//applications/app/sr_hmi_service/:sr_client_simulation", "features": []}, {"name": "log_to_dds", "dir": "//middleware/system/tools/log_to_libflow/:log_to_dds", "features": []}, {"name": "log_tools", "dir": "//middleware/system/tools/log_global_save/:log_tools", "features": []}, {"name": "takepoint_collect", "dir": "//applications/app/takepoint_collect:takepoint_collect_group", "features": []}, {"name": "takepoint_test", "dir": "//applications/app/takepoint_test:takepoint_test_group", "features": []}], "bak": [{"name": "sample_threadpool", "dir": "//middleware/system/core/libcppbase/demo:sample_threadpool", "features": []}, {"name": "sample_threadpool", "dir": "//middleware/system/core/libcppbase/demo:sample_threadpool", "features": []}, {"name": "state_manager_group", "dir": "//applications/app/state_manager:state_manager_group", "features": []}, {"name": "caninput_group", "dir": "//applications/app/caninput:caninput_group", "features": []}, {"name": "diag_manager_group", "dir": "//applications/app/diag_manager:diag_manager_group", "features": []}, {"name": "persistency", "dir": "//middleware/persistency:persistency_group", "features": []}, {"name": "system_res_monitor", "dir": "//middleware/system/tools/system_res_monitor/:system_res_monitor", "features": []}, {"name": "uss_radar_group", "dir": "//applications/app/uss_radar:uss_radar_group", "features": []}, {"name": "radar_group", "dir": "//applications/app/radar:radar_group", "features": []}, {"name": "adas_data_service_group", "dir": "//applications/app/adas_data_service:adas_data_service_group", "features": []}, {"name": "chassis_group", "dir": "//applications/app/chassis:chassis_group", "features": []}, {"name": "canout_bak_group", "dir": "//applications/app/canout_bak:canout_bak_group", "features": []}, {"name": "perceived_dds_convert", "dir": "//applications/app/example-protocol-convert:perceived_dds_convert", "features": []}]}]}