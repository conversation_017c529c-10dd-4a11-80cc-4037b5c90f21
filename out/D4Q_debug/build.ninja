ninja_required_version = 1.7.2

rule gn
  command = ../../build/minieye/tools/gn --root=../.. -q --dotfile=../../build/minieye/.gn --export-compile-commands gen .
  description = Regenerating ninja files

build build.ninja: gn
  generator = 1
  depfile = build.ninja.d

subninja toolchain.ninja

build AdasHmiTest: phony base/sr_hmi_service/bin/AdasHmiTest
build CanIoTest: phony bin/CanIoTest
build GnssRawReader: phony base/gnss/bin/GnssRawReader
build ImuClientTest: phony base/imu/bin/ImuClientTest
build JetouSdMapTest: phony base/sr_hmi_client/bin/JetouSdMapTest
build PairTest: phony bin/PairTest
build PubSubTest: phony bin/PubSubTest
build RMAgentTest: phony bin/RMAgentTest
build ReqRepTest: phony bin/ReqRepTest
build SurveyTest: phony bin/SurveyTest
build TestDataDds: phony base/caninput/bin/TestDataDds
build TestDataIpc: phony base/caninput/bin/TestDataIpc
build TestVehSignalHandler: phony base/caninput/bin/TestVehSignalHandler
build app_avm_out: phony base/vout/bin/app_avm_out
build avm_pym_stitch: phony base/stitch/bin/avm_pym_stitch
build backtrace_tool: phony bin/backtrace_tool
build broadcast_test: phony base/doip/bin/broadcast_test
build camera_client: phony base/camera/bin/camera_client
build camera_dds_recv: phony base/camera/bin/camera_dds_recv
build camera_encode_libflow: phony base/camera/bin/camera_encode_libflow
build camera_service: phony base/camera/bin/camera_service
build camera_tool: phony base/camera/bin/camera_tool
build canHaldump: phony bin/canHaldump
build can_view: phony base/flex_diagnosis/can_view
build candump: phony bin/candump
build caninput: phony base/caninput/bin/caninput
build canout: phony base/canout/bin/canout
build cansend: phony bin/cansend
build classtest_state: phony base/state_manager/bin/classtest_state
build client_radar: phony base/radar/client_radar
build daemon: phony bin/daemon
build dds_unittest: phony base/test/dds_test/bin/dds_unittest
build ddsreader_imu: phony base/imu/bin/ddsreader_imu
build ddsreader_test_caninput: phony base/caninput/bin/ddsreader_test_caninput
build ddswriter_imu: phony base/imu/bin/ddswriter_imu
build doip: phony base/doip/bin/doip
build dumprc: phony bin/dumprc
build dumpsys: phony bin/dumpsys
build dv_test: phony base/imu/bin/dv_test
build eth_diagnosis: phony base/eth_diagnosis/bin/eth_diagnosis
build eth_diagnosis_subscript: phony base/eth_diagnosis/bin/eth_diagnosis_subscript
build ets_service: phony base/ets_service/bin/ets_service
build fault_server: phony base/diagnostic/bin/fault_server
build file_monitor: phony bin/file_monitor
build flex_diagnosis: phony base/flex_diagnosis/flex_diagnosis
build flexidag_factory: phony base/bin/flexidag_factory
build get_dtc: phony bin/get_dtc
build get_version: phony bin/get_version
build getcameraimage: phony base/camera/bin/getcameraimage
build getshareconfig: phony bin/getshareconfig
build getsystemprop: phony bin/getsystemprop
build gnss: phony base/gnss/bin/gnss
build gtest: phony bin/gtest
build idvr: phony base/idvr/bin/idvr
build imu_app: phony base/imu/bin/imu_app
build imu_ota: phony base/imu/bin/imu_ota
build imu_recv_demo: phony base/imu/bin/imu_recv_demo
build imu_sim: phony base/imu/imu_sim/bin/imu_sim
build isotp_tester: phony base/flexidag_factory/isotp_tester
build log_to_dds: phony bin/log_to_dds
build log_to_libflow: phony bin/log_to_libflow
build logd: phony bin/logd
build logdcat: phony bin/logdcat
build mcap_reader: phony base/bin/mcap_reader
build mi_ota_tool: phony bin/mi_ota_tool
build mlogcat: phony bin/mlogcat
build mlogsend: phony bin/mlogsend
build modtool: phony base/gnss/bin/modtool
build nnTest: phony base/doip/bin/nnTest
build nnpubsub: phony base/nnflow/nnpubsub
build nnreqrep: phony base/nnflow/nnreqrep
build ota_burn: phony bin/ota_burn
build persistency: phony bin/persistency
build phm: phony base/phm/bin/phm
build phmAgtSample: phony bin/phmAgtSample
build plugin_service: phony base/sr_hmi_service/bin/plugin_service
build ppp_engine_app: phony base/qxids_sdk/bin/ppp_engine_app
build ppp_engine_app_bak: phony base/qxids_sdk/bin/ppp_engine_app_bak
build radar: phony base/radar/radar
build report_fault: phony base/diagnostic/bin/report_fault
build sample_dumpsys: phony bin/sample_dumpsys
build sample_em: phony bin/sample_em
build sample_json_test: phony bin/sample_json_test
build sample_logd: phony bin/sample_logd
build sample_message: phony bin/sample_message
build sample_mlog: phony bin/sample_mlog
build sample_nnflow: phony base/nnflow/sample_nnflow
build sample_timehal: phony bin/sample_timehal
build sample_tombstone: phony bin/sample_tombstone
build scantree: phony bin/scantree
build service_nssr: phony base/qxids_sdk/bin/service_nssr
build setshareconfig: phony bin/setshareconfig
build setsystemprop: phony bin/setsystemprop
build sim_dds_test: phony base/imu/imu_sim/bin/sim_dds_test
build someip_routing: phony base/sr_hmi_service/bin/someip_routing
build sr_client_simulation: phony base/sr_hmi_service/bin/sr_client_simulation
build sr_hmi_client: phony base/sr_hmi_client/bin/sr_hmi_client
build sr_hmi_service: phony base/sr_hmi_service/bin/sr_hmi_service
build sr_input_test: phony base/sr_hmi_service/bin/sr_input_test
build startrc: phony bin/startrc
build state_change_test: phony base/state_manager/bin/state_change_test
build state_client_test: phony base/state_manager/bin/state_client_test
build state_manager: phony base/state_manager/bin/state_manager
build stoprc: phony bin/stoprc
build sync_status: phony misc/tools/sync_status
build sys_perf_daemon: phony bin/sys_perf_daemon
build system_res_monitor: phony bin/system_res_monitor
build takepoint_collect: phony base/takepoint_collect/bin/takepoint_collect
build takepoint_collect_tools_decrypt: phony base/bin/takepoint_collect_tools_decrypt
build takepoint_test: phony base/takepoint_test/bin/takepoint_test
build tboxMsgTest: phony base/sr_hmi_client/bin/tboxMsgTest
build test: phony bin/test
build test_dds_reader: phony base/canout/bin/test_dds_reader
build test_dds_writer: phony base/canout/bin/test_dds_writer
build timesync: phony base/timesync/bin/timesync
build tombstone: phony bin/tombstone
build unittest: phony base/imu/bin/unittest
build unittest_diagnosis: phony base/diagnostic/bin/unittest_diagnosis
build unittest_state: phony base/state_manager/bin/unittest_state
build upper_tester: phony base/upper_tester/bin/upper_tester
build voutdiagnose: phony base/vout/bin/voutdiagnose
build PhmAgent: phony ./libPhmAgent.so
build SrDataSdk: phony ./libSrDataSdk.so
build StateAgent: phony ./libStateAgent.so
build StateTrigger: phony ./libStateTrigger.so
build app_avm_out_group: phony obj/applications/bsp_app/app_avm_out/app_avm_out_group.stamp
build archive-log: phony obj/middleware/system/tools/log_global_save/archive-log.stamp
build avm_pym_stitch_conf: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_conf.stamp
build avm_pym_stitch_group: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_group.stamp
build avm_pym_stitch_run: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_run.stamp
build backtrace_tombstone: phony ./libbacktrace_tombstone.a
build base_logd: phony ./libbase_logd.a
build base_tombstone: phony ./libbase_tombstone.a
build camera_encode_libflow_group: phony obj/applications/bsp_app/encode_libflow/camera_encode_libflow_group.stamp
build camera_run: phony obj/applications/bsp_app/camera_service/camera_run.stamp
build camera_service_conf: phony obj/applications/bsp_app/camera_service/camera_service_conf.stamp
build camera_service_group: phony obj/applications/bsp_app/camera_service/camera_service_group.stamp
build camera_test: phony obj/applications/bsp_app/camera_service/camera_test.stamp
build can_utils_group: phony obj/applications/bsp_app/can_utils/can_utils_group.stamp
build caninput_etc: phony obj/applications/app/caninput/caninput_etc.stamp
build caninput_group: phony obj/applications/app/caninput/caninput_group.stamp
build caninput_run: phony obj/applications/app/caninput/caninput_run.stamp
build caninput_test_run: phony obj/applications/app/caninput/caninput_test_run.stamp
build canout_etc: phony obj/applications/app/canout/canout_etc.stamp
build canout_group: phony obj/applications/app/canout/canout_group.stamp
build canout_run: phony obj/applications/app/canout/canout_run.stamp
build canout_test_run: phony obj/applications/app/canout/canout_test_run.stamp
build common_group: phony obj/applications/app/common/common_group.stamp
build cppbase: phony ./libcppbase.so
build daemon_group: phony obj/middleware/daemon/daemon_group.stamp
build daemon_sample: phony obj/middleware/daemon/sample/daemon_sample.stamp
build data_proto_o: phony ./libdata_proto_o.so
build dds_gtest_group: phony obj/applications/app/test/dds_gtest/dds_gtest_group.stamp
build demangle_tombstone: phony ./libdemangle_tombstone.a
build diagnosis: phony ./libdiagnosis.so
build diagnosis_group: phony obj/applications/app/diagnosis/diagnosis_group.stamp
build diagnostic_etc: phony obj/applications/app/diagnosis/diagnostic_etc.stamp
build doipNnSrv: phony ./libdoipNnSrv.so
build doip_config: phony obj/applications/app/doip/doip_config.stamp
build doip_group: phony obj/applications/app/doip/doip_group.stamp
build doip_sh: phony obj/applications/app/doip/doip_sh.stamp
build dumpsys_group: phony obj/middleware/dumpsys/dumpsys_group.stamp
build dv_run: phony obj/applications/app/imu/dv_run.stamp
build encode_run: phony obj/applications/bsp_app/camera_service/encode_run.stamp
build eth_diagnosis_etc: phony obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_etc.stamp
build eth_diagnosis_group: phony obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_group.stamp
build ets_service_etc: phony obj/middleware/ets_service/ets_service_etc.stamp
build ets_service_group: phony obj/middleware/ets_service/ets_service_group.stamp
build ets_service_run: phony obj/middleware/ets_service/ets_service_run.stamp
build filelog_demo: phony obj/middleware/system/core/filelog/demo/filelog_demo.stamp
build filelog_group: phony obj/middleware/system/core/filelog/filelog_group.stamp
build flex_diagnosis_etc: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_etc.stamp
build flex_diagnosis_group: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_group.stamp
build flex_diagnosis_tools: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_tools.stamp
build flexidag_factory_etc: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_etc.stamp
build flexidag_factory_group: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_group.stamp
build flexidag_factory_shell: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_shell.stamp
build get_mcu_version_shell: phony obj/applications/bsp_app/flexidag_factory/get_mcu_version_shell.stamp
build gnss_group: phony obj/applications/app/gnss/gnss_group.stamp
build gnss_sync_to_mcu_phc: phony obj/applications/bsp_app/timesync/gnss_sync_to_mcu_phc.stamp
build gpu_performance_dump: phony obj/applications/bsp_app/sys_perf_daemon/gpu_performance_dump.stamp
build idvr_group: phony obj/applications/app/idvr/idvr_group.stamp
build imu_etc: phony obj/applications/app/imu/imu_etc.stamp
build imu_group: phony obj/applications/app/imu/imu_group.stamp
build imu_run: phony obj/applications/app/imu/imu_run.stamp
build imu_sim_etc: phony obj/applications/app/imu/utils/imu_sim_etc.stamp
build imu_sim_readme: phony obj/applications/app/imu/utils/imu_sim_readme.stamp
build imu_sim_run: phony obj/applications/app/imu/utils/imu_sim_run.stamp
build imu_test_run: phony obj/applications/app/imu/imu_test_run.stamp
build jsonUtil: phony ./libjsonUtil.so
build jsonUtil_group: phony obj/middleware/system/core/libjsonUtil/jsonUtil_group.stamp
build libCameraClient: phony ./libCameraClient.so
build libDoIP: phony ./libDoIP.so
build libDoIP_group: phony obj/applications/app/doip/src/libDoIP/libDoIP_group.stamp
build libImuClient: phony ./libImuClient.so
build libRMAgent: phony ./libRMAgent.so
build libRMAgent_group: phony obj/applications/app/common/libRMAgent/libRMAgent_group.stamp
build libSigVerify: phony ./libSigVerify.so
build libUDS: phony ./libUDS.so
build libUDS_group: phony obj/applications/app/doip/src/libUDS/libUDS_group.stamp
build libUdsOnDoIP: phony ./libUdsOnDoIP.so
build libUdsOnDoIP_group: phony obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP_group.stamp
build libUniComm: phony ./libUniComm.so
build libabinstaller_static: phony obj/applications/bsp_app/ota/libabinstaller_static.stamp
build libabinstallerpl_static: phony obj/applications/bsp_app/ota/libabinstallerpl_static.stamp
build libapa_plugin_example: phony ./libapa_plugin_example.so
build libcanio: phony ./libcanio.so
build libcollect: phony ./libcollect.so
build libcommon_utils: phony ./libcommon_utils.so
build libcommon_utils_group: phony obj/applications/app/common/utils/libcommon_utils_group.stamp
build libcutils_logd: phony ./libcutils_logd.a
build libcutils_tombstone: phony ./libcutils_tombstone.a
build libdbc: phony ./libdbc.so
build libdbc_group: phony obj/applications/app/common/dbc/libdbc_group.stamp
build libdecode_group: phony obj/applications/bsp_app/libdecode/libdecode_group.stamp
build libdumpsys_interface: phony ./libdumpsys_interface.so
build libevutil: phony ./libevutil.so
build libfilelog: phony ./libfilelog.so
build liblogd: phony ./liblogd.so
build liblogd_static: phony ./liblogd_static.a
build libminieyetimehal: phony ./libminieyetimehal.so
build libminieyetimehal_group: phony obj/applications/app/common/timehal/libminieyetimehal_group.stamp
build libmlog: phony ./libmlog.so
build libmlog_static: phony ./libmlog_static.a
build libnnmsg: phony ./libnnmsg.so
build libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build libpcre_logd: phony ./libpcre_logd.a
build libpcrecpp_logd: phony ./libpcrecpp_logd.a
build libpersistency: phony ./libpersistency.so
build libpersistency_common: phony ./libpersistency_common.so
build libproto_group: phony obj/applications/app/common/pb/libproto_group.stamp
build library_canio_group: phony obj/applications/app/common/canio/library_canio_group.stamp
build library_nanomsg_group: phony obj/middleware/communication/nanomsg/library_nanomsg_group.stamp
build librtklib: phony libs/librtklib.a
build librtosdecmjpegV5: phony ./librtosdecmjpegV5.so
build libsensor_status_plugin: phony ./libsensor_status_plugin.so
build libsr_core_manager: phony ./libsr_core_manager.so
build libsysutils_logd: phony ./libsysutils_logd.a
build libupgrade_api: phony ./libupgrade_api.so
build libvehicle_plugin_example: phony ./libvehicle_plugin_example.so
build log_tools: phony obj/middleware/system/tools/log_global_save/log_tools.stamp
build logd_group: phony obj/middleware/logd/logd_group.stamp
build lzma_tombstone: phony ./liblzma_tombstone.a
build mcu_phc_sync_to_system: phony obj/applications/bsp_app/timesync/mcu_phc_sync_to_system.stamp
build message: phony ./libmessage.so
build minieye-log: phony obj/middleware/system/tools/log_global_save/minieye-log.stamp
build mlog_group: phony obj/middleware/mlog/mlog_group.stamp
build mlog_sample: phony obj/middleware/mlog/sample/mlog_sample.stamp
build nnflow: phony ./libnnflow.so
build nnflow_group: phony obj/middleware/communication/libnnflow/nnflow_group.stamp
build nssr_run: phony obj/applications/app/qxids_sdk/nssr_run.stamp
build ota_lib: phony obj/applications/app/imu/utils/ota_lib.stamp
build ota_run: phony obj/applications/app/imu/utils/ota_run.stamp
build persistency_group: phony obj/middleware/persistency/persistency_group.stamp
build phm_cfg_dir: phony obj/applications/app/phm/phm_cfg_dir.stamp
build phm_group: phony obj/applications/app/phm/phm_group.stamp
build pppe_run: phony obj/applications/app/qxids_sdk/pppe_run.stamp
build procinfo_tombstone: phony ./libprocinfo_tombstone.a
build property: phony ./libproperty.so
build qxids_sdk_etc: phony obj/applications/app/qxids_sdk/qxids_sdk_etc.stamp
build qxids_sdk_group: phony obj/applications/app/qxids_sdk/qxids_sdk_group.stamp
build qxids_sdk_libs: phony obj/applications/app/qxids_sdk/qxids_sdk_libs.stamp
build qxids_sdk_run: phony obj/applications/app/qxids_sdk/qxids_sdk_run.stamp
build qxids_sdk_test_run: phony obj/applications/app/qxids_sdk/qxids_sdk_test_run.stamp
build qxids_sdk_test_sh: phony obj/applications/app/qxids_sdk/qxids_sdk_test_sh.stamp
build radar_etc: phony obj/applications/app/radar/radar_etc.stamp
build radar_group: phony obj/applications/app/radar/radar_group.stamp
build radar_run: phony obj/applications/app/radar/radar_run.stamp
build radar_test_sh: phony obj/applications/app/radar/radar_test_sh.stamp
build routing_run: phony obj/applications/app/sr_hmi_service/routing_run.stamp
build rtc_sync_to_phc0: phony obj/applications/bsp_app/timesync/rtc_sync_to_phc0.stamp
build run_camera_adas: phony obj/applications/bsp_app/camera_service/run_camera_adas.stamp
build run_camera_apa: phony obj/applications/bsp_app/camera_service/run_camera_apa.stamp
build run_sh: phony obj/applications/app/phm/run_sh.stamp
build sample_json: phony obj/applications/app/canout/test/sample_json.stamp
build sdk_version_file: phony obj/applications/app/qxids_sdk/sdk_version_file.stamp
build shell1: phony obj/applications/app/gnss/shell1.stamp
build shell2: phony obj/applications/app/gnss/shell2.stamp
build shell3: phony obj/applications/app/gnss/shell3.stamp
build sr_adashmi_run: phony obj/applications/app/sr_hmi_service/sample/sr_adashmi_run.stamp
build sr_hmi_client_cfg: phony obj/applications/app/sr_hmi_client/sr_hmi_client_cfg.stamp
build sr_hmi_client_group: phony obj/applications/app/sr_hmi_client/sr_hmi_client_group.stamp
build sr_hmi_client_run: phony obj/applications/app/sr_hmi_client/sr_hmi_client_run.stamp
build sr_hmi_plugin_cfg: phony obj/applications/app/sr_hmi_service/src/plugin/sr_hmi_plugin_cfg.stamp
build sr_hmi_service_cfg: phony obj/applications/app/sr_hmi_service/sr_hmi_service_cfg.stamp
build sr_hmi_service_run: phony obj/applications/app/sr_hmi_service/sr_hmi_service_run.stamp
build sr_plugin_group: phony obj/applications/app/sr_hmi_service/src/plugin/sr_plugin_group.stamp
build sr_sample_group: phony obj/applications/app/sr_hmi_service/sample/sr_sample_group.stamp
build sr_service_group: phony obj/applications/app/sr_hmi_service/sr_service_group.stamp
build state_manager_etc: phony obj/applications/app/state_manager/state_manager_etc.stamp
build state_manager_group: phony obj/applications/app/state_manager/state_manager_group.stamp
build state_manager_run: phony obj/applications/app/state_manager/state_manager_run.stamp
build state_test: phony obj/applications/app/state_manager/test/state_test.stamp
build stresstest_run: phony obj/applications/app/state_manager/test/stresstest_run.stamp
build symlink_vsomeip3: phony obj/middleware/someip/symlink_vsomeip3.stamp
build symlink_vsomeip3_cfg: phony obj/middleware/someip/symlink_vsomeip3_cfg.stamp
build symlink_vsomeip3_e2e: phony obj/middleware/someip/symlink_vsomeip3_e2e.stamp
build symlink_vsomeip3_sd: phony obj/middleware/someip/symlink_vsomeip3_sd.stamp
build sys_perf_daemon_group: phony obj/applications/bsp_app/sys_perf_daemon/sys_perf_daemon_group.stamp
build takepoint_collect_config: phony obj/applications/app/takepoint_collect/takepoint_collect_config.stamp
build takepoint_collect_group: phony obj/applications/app/takepoint_collect/takepoint_collect_group.stamp
build takepoint_collect_sh: phony obj/applications/app/takepoint_collect/takepoint_collect_sh.stamp
build takepoint_collect_util: phony obj/applications/app/takepoint_collect/takepoint_collect_util.stamp
build takepoint_test_group: phony obj/applications/app/takepoint_test/takepoint_test_group.stamp
build takepoint_test_sh: phony obj/applications/app/takepoint_test/takepoint_test_sh.stamp
build takepoint_thirdparty: phony obj/applications/app/takepoint_collect/takepoint_thirdparty.stamp
build test_group: phony obj/applications/app/test/test_group.stamp
build timesync_config_etc: phony obj/applications/bsp_app/timesync/timesync_config_etc.stamp
build timesync_etc: phony obj/applications/bsp_app/timesync/timesync_etc.stamp
build timesync_group: phony obj/applications/bsp_app/timesync/timesync_group.stamp
build timesync_test_sh: phony obj/applications/bsp_app/timesync/timesync_test_sh.stamp
build tombstone_client: phony ./libtombstone_client.so
build tombstone_group: phony obj/middleware/tombstone/tombstone_group.stamp
build unwindstack_tombstone: phony ./libunwindstack_tombstone.a
build upgrade_group: phony obj/applications/bsp_app/ota/upgrade_group.stamp
build upper_tester_group: phony obj/middleware/upper_tester/upper_tester_group.stamp
build ut_run: phony obj/middleware/upper_tester/ut_run.stamp
build vout_config: phony obj/applications/bsp_app/app_avm_out/vout_config.stamp
build vout_run: phony obj/applications/bsp_app/app_avm_out/vout_run.stamp
build vsomeip3: phony ./libvsomeip3.so
build vsomeip3-cfg: phony ./libvsomeip3-cfg.so
build vsomeip3-e2e: phony ./libvsomeip3-e2e.so
build vsomeip3-sd: phony ./libvsomeip3-sd.so
build vsomeip3_group: phony obj/middleware/someip/vsomeip3_group.stamp
build applications/app/caninput$:TestDataDds: phony base/caninput/bin/TestDataDds
build applications/app/caninput$:TestDataIpc: phony base/caninput/bin/TestDataIpc
build applications/app/caninput$:TestVehSignalHandler: phony base/caninput/bin/TestVehSignalHandler
build applications/app/caninput$:caninput: phony base/caninput/bin/caninput
build applications/app/caninput: phony base/caninput/bin/caninput
build applications/app/caninput$:caninput_etc: phony obj/applications/app/caninput/caninput_etc.stamp
build applications/app/caninput$:caninput_group: phony obj/applications/app/caninput/caninput_group.stamp
build applications/app/caninput$:caninput_run: phony obj/applications/app/caninput/caninput_run.stamp
build applications/app/caninput$:caninput_test_run: phony obj/applications/app/caninput/caninput_test_run.stamp
build applications/app/caninput$:ddsreader_test_caninput: phony base/caninput/bin/ddsreader_test_caninput
build applications/app/caninput$:module_test: phony base/caninput/bin/module_test
build applications/app/canout$:canout: phony base/canout/bin/canout
build applications/app/canout: phony base/canout/bin/canout
build applications/app/canout$:canout_etc: phony obj/applications/app/canout/canout_etc.stamp
build applications/app/canout$:canout_group: phony obj/applications/app/canout/canout_group.stamp
build applications/app/canout$:canout_run: phony obj/applications/app/canout/canout_run.stamp
build applications/app/canout$:canout_test_run: phony obj/applications/app/canout/canout_test_run.stamp
build applications/app/canout/test$:ddsreader_test: phony base/canout/bin/ddsreader_test
build applications/app/canout/test$:ddswriter_test: phony base/canout/bin/ddswriter_test
build applications/app/canout/test$:module_test: phony base/canout/bin/module_test
build applications/app/canout/test$:sample_json: phony obj/applications/app/canout/test/sample_json.stamp
build applications/app/canout/test$:test: phony obj/applications/app/canout/test/test.stamp
build applications/app/canout/test: phony obj/applications/app/canout/test/test.stamp
build applications/app/canout/test$:test_dds_reader: phony base/canout/bin/test_dds_reader
build applications/app/canout/test$:test_dds_writer: phony base/canout/bin/test_dds_writer
build applications/app/common$:common_group: phony obj/applications/app/common/common_group.stamp
build applications/app/common/canio$:CanIoTest: phony bin/CanIoTest
build applications/app/common/canio$:canHaldump: phony bin/canHaldump
build applications/app/common/canio$:libcanio: phony ./libcanio.so
build applications/app/common/canio$:library_canio_group: phony obj/applications/app/common/canio/library_canio_group.stamp
build applications/app/common/dbc$:libdbc: phony ./libdbc.so
build applications/app/common/dbc$:libdbc_group: phony obj/applications/app/common/dbc/libdbc_group.stamp
build applications/app/common/libRMAgent$:RMAgentTest: phony bin/RMAgentTest
build applications/app/common/libRMAgent$:get_version: phony bin/get_version
build applications/app/common/libRMAgent$:libRMAgent: phony ./libRMAgent.so
build applications/app/common/libRMAgent: phony ./libRMAgent.so
build applications/app/common/libRMAgent$:libRMAgent_group: phony obj/applications/app/common/libRMAgent/libRMAgent_group.stamp
build applications/app/common/libSigVerify$:libSigVerify: phony ./libSigVerify.so
build applications/app/common/libSigVerify: phony ./libSigVerify.so
build applications/app/common/libUniComm$:libUniComm: phony ./libUniComm.so
build applications/app/common/libUniComm: phony ./libUniComm.so
build applications/app/common/libcollect$:libcollect: phony ./libcollect.so
build applications/app/common/libcollect: phony ./libcollect.so
build applications/app/common/pb$:data_proto_o: phony ./libdata_proto_o.so
build applications/app/common/pb$:libproto_group: phony obj/applications/app/common/pb/libproto_group.stamp
build applications/app/common/timehal$:libminieyetimehal: phony ./libminieyetimehal.so
build applications/app/common/timehal$:libminieyetimehal_group: phony obj/applications/app/common/timehal/libminieyetimehal_group.stamp
build applications/app/common/timehal$:sample_timehal: phony bin/sample_timehal
build applications/app/common/utils$:libcommon_utils: phony ./libcommon_utils.so
build applications/app/common/utils$:libcommon_utils_group: phony obj/applications/app/common/utils/libcommon_utils_group.stamp
build applications/app/diagnosis$:diagnosis: phony ./libdiagnosis.so
build applications/app/diagnosis: phony ./libdiagnosis.so
build applications/app/diagnosis$:diagnosis_group: phony obj/applications/app/diagnosis/diagnosis_group.stamp
build applications/app/diagnosis$:diagnostic_etc: phony obj/applications/app/diagnosis/diagnostic_etc.stamp
build applications/app/diagnosis$:fault_server: phony base/diagnostic/bin/fault_server
build applications/app/diagnosis$:report_fault: phony base/diagnostic/bin/report_fault
build applications/app/diagnosis$:shell: phony obj/applications/app/diagnosis/shell.stamp
build applications/app/diagnosis$:unittest_diagnosis: phony base/diagnostic/bin/unittest_diagnosis
build applications/app/doip$:broadcast_test: phony base/doip/bin/broadcast_test
build applications/app/doip$:doip: phony base/doip/bin/doip
build applications/app/doip: phony base/doip/bin/doip
build applications/app/doip$:doipNnSrv: phony ./libdoipNnSrv.so
build applications/app/doip$:doip_config: phony obj/applications/app/doip/doip_config.stamp
build applications/app/doip$:doip_group: phony obj/applications/app/doip/doip_group.stamp
build applications/app/doip$:doip_sh: phony obj/applications/app/doip/doip_sh.stamp
build applications/app/doip$:get_dtc: phony bin/get_dtc
build applications/app/doip$:nnTest: phony base/doip/bin/nnTest
build applications/app/doip/src/libDoIP$:libDoIP: phony ./libDoIP.so
build applications/app/doip/src/libDoIP: phony ./libDoIP.so
build applications/app/doip/src/libDoIP$:libDoIP_group: phony obj/applications/app/doip/src/libDoIP/libDoIP_group.stamp
build applications/app/doip/src/libUDS$:libUDS: phony ./libUDS.so
build applications/app/doip/src/libUDS: phony ./libUDS.so
build applications/app/doip/src/libUDS$:libUDS_group: phony obj/applications/app/doip/src/libUDS/libUDS_group.stamp
build applications/app/doip/src/libUdsOnDoIP$:libUdsOnDoIP: phony ./libUdsOnDoIP.so
build applications/app/doip/src/libUdsOnDoIP: phony ./libUdsOnDoIP.so
build applications/app/doip/src/libUdsOnDoIP$:libUdsOnDoIP_group: phony obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP_group.stamp
build applications/app/gnss$:GnssRawReader: phony base/gnss/bin/GnssRawReader
build applications/app/gnss$:gnss: phony base/gnss/bin/gnss
build applications/app/gnss: phony base/gnss/bin/gnss
build applications/app/gnss$:gnss_group: phony obj/applications/app/gnss/gnss_group.stamp
build applications/app/gnss$:librtklib: phony libs/librtklib.a
build applications/app/gnss$:modtool: phony base/gnss/bin/modtool
build applications/app/gnss$:shell: phony obj/applications/app/gnss/shell.stamp
build applications/app/gnss$:shell1: phony obj/applications/app/gnss/shell1.stamp
build applications/app/gnss$:shell2: phony obj/applications/app/gnss/shell2.stamp
build applications/app/gnss$:shell3: phony obj/applications/app/gnss/shell3.stamp
build applications/app/gnss$:this_conf_dir: phony obj/applications/app/gnss/this_conf_dir.stamp
build applications/app/idvr$:idvr: phony base/idvr/bin/idvr
build applications/app/idvr: phony base/idvr/bin/idvr
build applications/app/idvr$:idvr_group: phony obj/applications/app/idvr/idvr_group.stamp
build applications/app/idvr$:shell: phony obj/applications/app/idvr/shell.stamp
build applications/app/idvr$:this_conf_dir: phony obj/applications/app/idvr/this_conf_dir.stamp
build applications/app/imu$:ImuClientTest: phony base/imu/bin/ImuClientTest
build applications/app/imu$:ddsreader_imu: phony base/imu/bin/ddsreader_imu
build applications/app/imu$:ddswriter_imu: phony base/imu/bin/ddswriter_imu
build applications/app/imu$:dv_run: phony obj/applications/app/imu/dv_run.stamp
build applications/app/imu$:dv_test: phony base/imu/bin/dv_test
build applications/app/imu$:imu_app: phony base/imu/bin/imu_app
build applications/app/imu$:imu_etc: phony obj/applications/app/imu/imu_etc.stamp
build applications/app/imu$:imu_group: phony obj/applications/app/imu/imu_group.stamp
build applications/app/imu$:imu_recv_demo: phony base/imu/bin/imu_recv_demo
build applications/app/imu$:imu_run: phony obj/applications/app/imu/imu_run.stamp
build applications/app/imu$:imu_test_run: phony obj/applications/app/imu/imu_test_run.stamp
build applications/app/imu$:libImuClient: phony ./libImuClient.so
build applications/app/imu$:module_test: phony base/imu/bin/module_test
build applications/app/imu$:unittest: phony base/imu/bin/unittest
build applications/app/imu/utils$:imu_ota: phony base/imu/bin/imu_ota
build applications/app/imu/utils$:imu_sim: phony base/imu/imu_sim/bin/imu_sim
build applications/app/imu/utils$:imu_sim_etc: phony obj/applications/app/imu/utils/imu_sim_etc.stamp
build applications/app/imu/utils$:imu_sim_readme: phony obj/applications/app/imu/utils/imu_sim_readme.stamp
build applications/app/imu/utils$:imu_sim_run: phony obj/applications/app/imu/utils/imu_sim_run.stamp
build applications/app/imu/utils$:ota_lib: phony obj/applications/app/imu/utils/ota_lib.stamp
build applications/app/imu/utils$:ota_run: phony obj/applications/app/imu/utils/ota_run.stamp
build applications/app/imu/utils$:sim_dds_test: phony base/imu/imu_sim/bin/sim_dds_test
build applications/app/imu/utils$:utils: phony obj/applications/app/imu/utils/utils.stamp
build applications/app/imu/utils: phony obj/applications/app/imu/utils/utils.stamp
build applications/app/phm$:PhmAgent: phony ./libPhmAgent.so
build applications/app/phm$:gtest: phony bin/gtest
build applications/app/phm$:phm: phony base/phm/bin/phm
build applications/app/phm: phony base/phm/bin/phm
build applications/app/phm$:phmAgtSample: phony bin/phmAgtSample
build applications/app/phm$:phm_cfg_dir: phony obj/applications/app/phm/phm_cfg_dir.stamp
build applications/app/phm$:phm_group: phony obj/applications/app/phm/phm_group.stamp
build applications/app/phm$:run_sh: phony obj/applications/app/phm/run_sh.stamp
build applications/app/qxids_sdk$:ddsreader_test: phony base/qxids_sdk/bin/ddsreader_test
build applications/app/qxids_sdk$:ddswriter_test: phony base/qxids_sdk/bin/ddswriter_test
build applications/app/qxids_sdk$:module_test: phony base/qxids_sdk/bin/module_test
build applications/app/qxids_sdk$:nssr_run: phony obj/applications/app/qxids_sdk/nssr_run.stamp
build applications/app/qxids_sdk$:ppp_engine_app: phony base/qxids_sdk/bin/ppp_engine_app
build applications/app/qxids_sdk$:ppp_engine_app_bak: phony base/qxids_sdk/bin/ppp_engine_app_bak
build applications/app/qxids_sdk$:pppe_run: phony obj/applications/app/qxids_sdk/pppe_run.stamp
build applications/app/qxids_sdk$:qxids_sdk_etc: phony obj/applications/app/qxids_sdk/qxids_sdk_etc.stamp
build applications/app/qxids_sdk$:qxids_sdk_group: phony obj/applications/app/qxids_sdk/qxids_sdk_group.stamp
build applications/app/qxids_sdk$:qxids_sdk_libs: phony obj/applications/app/qxids_sdk/qxids_sdk_libs.stamp
build applications/app/qxids_sdk$:qxids_sdk_run: phony obj/applications/app/qxids_sdk/qxids_sdk_run.stamp
build applications/app/qxids_sdk$:qxids_sdk_test_run: phony obj/applications/app/qxids_sdk/qxids_sdk_test_run.stamp
build applications/app/qxids_sdk$:qxids_sdk_test_sh: phony obj/applications/app/qxids_sdk/qxids_sdk_test_sh.stamp
build applications/app/qxids_sdk$:sdk_version_file: phony obj/applications/app/qxids_sdk/sdk_version_file.stamp
build applications/app/qxids_sdk$:service_nssr: phony base/qxids_sdk/bin/service_nssr
build applications/app/radar$:radar: phony base/radar/radar
build applications/app/radar: phony base/radar/radar
build applications/app/radar$:radar_etc: phony obj/applications/app/radar/radar_etc.stamp
build applications/app/radar$:radar_group: phony obj/applications/app/radar/radar_group.stamp
build applications/app/radar$:radar_run: phony obj/applications/app/radar/radar_run.stamp
build applications/app/radar$:radar_test_sh: phony obj/applications/app/radar/radar_test_sh.stamp
build applications/app/radar/test$:UintTest: phony base/radar/UintTest
build applications/app/radar/test$:client_radar: phony base/radar/client_radar
build applications/app/sr_hmi_client$:JetouSdMapTest: phony base/sr_hmi_client/bin/JetouSdMapTest
build applications/app/sr_hmi_client$:sr_hmi_client: phony base/sr_hmi_client/bin/sr_hmi_client
build applications/app/sr_hmi_client: phony base/sr_hmi_client/bin/sr_hmi_client
build applications/app/sr_hmi_client$:sr_hmi_client_cfg: phony obj/applications/app/sr_hmi_client/sr_hmi_client_cfg.stamp
build applications/app/sr_hmi_client$:sr_hmi_client_group: phony obj/applications/app/sr_hmi_client/sr_hmi_client_group.stamp
build applications/app/sr_hmi_client$:sr_hmi_client_run: phony obj/applications/app/sr_hmi_client/sr_hmi_client_run.stamp
build applications/app/sr_hmi_client$:tboxMsgTest: phony base/sr_hmi_client/bin/tboxMsgTest
build applications/app/sr_hmi_service$:SrDataSdk: phony ./libSrDataSdk.so
build applications/app/sr_hmi_service$:libsensor_status_plugin: phony ./libsensor_status_plugin.so
build applications/app/sr_hmi_service$:routing_run: phony obj/applications/app/sr_hmi_service/routing_run.stamp
build applications/app/sr_hmi_service$:someip_routing: phony base/sr_hmi_service/bin/someip_routing
build applications/app/sr_hmi_service$:sr_client_simulation: phony base/sr_hmi_service/bin/sr_client_simulation
build applications/app/sr_hmi_service$:sr_hmi_service: phony base/sr_hmi_service/bin/sr_hmi_service
build applications/app/sr_hmi_service: phony base/sr_hmi_service/bin/sr_hmi_service
build applications/app/sr_hmi_service$:sr_hmi_service_cfg: phony obj/applications/app/sr_hmi_service/sr_hmi_service_cfg.stamp
build applications/app/sr_hmi_service$:sr_hmi_service_run: phony obj/applications/app/sr_hmi_service/sr_hmi_service_run.stamp
build applications/app/sr_hmi_service$:sr_input_test: phony base/sr_hmi_service/bin/sr_input_test
build applications/app/sr_hmi_service$:sr_service_group: phony obj/applications/app/sr_hmi_service/sr_service_group.stamp
build applications/app/sr_hmi_service/sample$:AdasHmiTest: phony base/sr_hmi_service/bin/AdasHmiTest
build applications/app/sr_hmi_service/sample$:sr_adashmi_run: phony obj/applications/app/sr_hmi_service/sample/sr_adashmi_run.stamp
build applications/app/sr_hmi_service/sample$:sr_sample_group: phony obj/applications/app/sr_hmi_service/sample/sr_sample_group.stamp
build applications/app/sr_hmi_service/src/plugin$:libapa_plugin_example: phony ./libapa_plugin_example.so
build applications/app/sr_hmi_service/src/plugin$:libsr_core_manager: phony ./libsr_core_manager.so
build applications/app/sr_hmi_service/src/plugin$:libvehicle_plugin_example: phony ./libvehicle_plugin_example.so
build applications/app/sr_hmi_service/src/plugin$:plugin_service: phony base/sr_hmi_service/bin/plugin_service
build applications/app/sr_hmi_service/src/plugin$:sr_hmi_plugin_cfg: phony obj/applications/app/sr_hmi_service/src/plugin/sr_hmi_plugin_cfg.stamp
build applications/app/sr_hmi_service/src/plugin$:sr_plugin_group: phony obj/applications/app/sr_hmi_service/src/plugin/sr_plugin_group.stamp
build applications/app/state_manager$:StateAgent: phony ./libStateAgent.so
build applications/app/state_manager$:StateTrigger: phony ./libStateTrigger.so
build applications/app/state_manager$:state_change_test: phony base/state_manager/bin/state_change_test
build applications/app/state_manager$:state_client_test: phony base/state_manager/bin/state_client_test
build applications/app/state_manager$:state_manager: phony base/state_manager/bin/state_manager
build applications/app/state_manager: phony base/state_manager/bin/state_manager
build applications/app/state_manager$:state_manager_etc: phony obj/applications/app/state_manager/state_manager_etc.stamp
build applications/app/state_manager$:state_manager_group: phony obj/applications/app/state_manager/state_manager_group.stamp
build applications/app/state_manager$:state_manager_run: phony obj/applications/app/state_manager/state_manager_run.stamp
build applications/app/state_manager/test$:classtest_state: phony base/state_manager/bin/classtest_state
build applications/app/state_manager/test$:state_test: phony obj/applications/app/state_manager/test/state_test.stamp
build applications/app/state_manager/test$:stresstest_run: phony obj/applications/app/state_manager/test/stresstest_run.stamp
build applications/app/state_manager/test$:unittest_state: phony base/state_manager/bin/unittest_state
build applications/app/takepoint_collect$:mcap_reader: phony base/bin/mcap_reader
build applications/app/takepoint_collect$:takepoint_collect: phony base/takepoint_collect/bin/takepoint_collect
build applications/app/takepoint_collect: phony base/takepoint_collect/bin/takepoint_collect
build applications/app/takepoint_collect$:takepoint_collect_config: phony obj/applications/app/takepoint_collect/takepoint_collect_config.stamp
build applications/app/takepoint_collect$:takepoint_collect_group: phony obj/applications/app/takepoint_collect/takepoint_collect_group.stamp
build applications/app/takepoint_collect$:takepoint_collect_sh: phony obj/applications/app/takepoint_collect/takepoint_collect_sh.stamp
build applications/app/takepoint_collect$:takepoint_collect_tools_decrypt: phony base/bin/takepoint_collect_tools_decrypt
build applications/app/takepoint_collect$:takepoint_collect_util: phony obj/applications/app/takepoint_collect/takepoint_collect_util.stamp
build applications/app/takepoint_collect$:takepoint_thirdparty: phony obj/applications/app/takepoint_collect/takepoint_thirdparty.stamp
build applications/app/takepoint_test$:takepoint_test: phony base/takepoint_test/bin/takepoint_test
build applications/app/takepoint_test: phony base/takepoint_test/bin/takepoint_test
build applications/app/takepoint_test$:takepoint_test_group: phony obj/applications/app/takepoint_test/takepoint_test_group.stamp
build applications/app/takepoint_test$:takepoint_test_sh: phony obj/applications/app/takepoint_test/takepoint_test_sh.stamp
build applications/app/test$:test_group: phony obj/applications/app/test/test_group.stamp
build applications/app/test/dds_gtest$:dds_gtest_group: phony obj/applications/app/test/dds_gtest/dds_gtest_group.stamp
build applications/app/test/dds_gtest$:dds_unittest: phony base/test/dds_test/bin/dds_unittest
build applications/bsp_app/app_avm_out$:app_avm_out: phony base/vout/bin/app_avm_out
build applications/bsp_app/app_avm_out: phony base/vout/bin/app_avm_out
build applications/bsp_app/app_avm_out$:app_avm_out_group: phony obj/applications/bsp_app/app_avm_out/app_avm_out_group.stamp
build applications/bsp_app/app_avm_out$:vout_config: phony obj/applications/bsp_app/app_avm_out/vout_config.stamp
build applications/bsp_app/app_avm_out$:vout_run: phony obj/applications/bsp_app/app_avm_out/vout_run.stamp
build applications/bsp_app/app_avm_out$:voutdiagnose: phony base/vout/bin/voutdiagnose
build applications/bsp_app/avm_pym_stitch$:avm_pym_stitch: phony base/stitch/bin/avm_pym_stitch
build applications/bsp_app/avm_pym_stitch: phony base/stitch/bin/avm_pym_stitch
build applications/bsp_app/avm_pym_stitch$:avm_pym_stitch_conf: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_conf.stamp
build applications/bsp_app/avm_pym_stitch$:avm_pym_stitch_group: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_group.stamp
build applications/bsp_app/avm_pym_stitch$:avm_pym_stitch_run: phony obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_run.stamp
build applications/bsp_app/camera_service$:UintTest: phony base/camera/bin/UintTest
build applications/bsp_app/camera_service$:camera_client: phony base/camera/bin/camera_client
build applications/bsp_app/camera_service$:camera_run: phony obj/applications/bsp_app/camera_service/camera_run.stamp
build applications/bsp_app/camera_service$:camera_service: phony base/camera/bin/camera_service
build applications/bsp_app/camera_service: phony base/camera/bin/camera_service
build applications/bsp_app/camera_service$:camera_service_conf: phony obj/applications/bsp_app/camera_service/camera_service_conf.stamp
build applications/bsp_app/camera_service$:camera_service_group: phony obj/applications/bsp_app/camera_service/camera_service_group.stamp
build applications/bsp_app/camera_service$:camera_test: phony obj/applications/bsp_app/camera_service/camera_test.stamp
build applications/bsp_app/camera_service$:encode_run: phony obj/applications/bsp_app/camera_service/encode_run.stamp
build applications/bsp_app/camera_service$:libCameraClient: phony ./libCameraClient.so
build applications/bsp_app/camera_service$:run_camera_adas: phony obj/applications/bsp_app/camera_service/run_camera_adas.stamp
build applications/bsp_app/camera_service$:run_camera_apa: phony obj/applications/bsp_app/camera_service/run_camera_apa.stamp
build applications/bsp_app/camera_service/tools$:camera_dds_recv: phony base/camera/bin/camera_dds_recv
build applications/bsp_app/camera_service/tools$:camera_tool: phony base/camera/bin/camera_tool
build applications/bsp_app/camera_service/tools$:getcameraimage: phony base/camera/bin/getcameraimage
build applications/bsp_app/can_utils$:can_utils_group: phony obj/applications/bsp_app/can_utils/can_utils_group.stamp
build applications/bsp_app/can_utils$:candump: phony bin/candump
build applications/bsp_app/can_utils$:cansend: phony bin/cansend
build applications/bsp_app/encode_libflow$:camera_encode_libflow: phony base/camera/bin/camera_encode_libflow
build applications/bsp_app/encode_libflow$:camera_encode_libflow_group: phony obj/applications/bsp_app/encode_libflow/camera_encode_libflow_group.stamp
build applications/bsp_app/eth_diagnosis$:eth_diagnosis: phony base/eth_diagnosis/bin/eth_diagnosis
build applications/bsp_app/eth_diagnosis: phony base/eth_diagnosis/bin/eth_diagnosis
build applications/bsp_app/eth_diagnosis$:eth_diagnosis_etc: phony obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_etc.stamp
build applications/bsp_app/eth_diagnosis$:eth_diagnosis_group: phony obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_group.stamp
build applications/bsp_app/eth_diagnosis$:eth_diagnosis_subscript: phony base/eth_diagnosis/bin/eth_diagnosis_subscript
build applications/bsp_app/flex_diagnosis$:can_view: phony base/flex_diagnosis/can_view
build applications/bsp_app/flex_diagnosis$:flex_diagnosis: phony base/flex_diagnosis/flex_diagnosis
build applications/bsp_app/flex_diagnosis: phony base/flex_diagnosis/flex_diagnosis
build applications/bsp_app/flex_diagnosis$:flex_diagnosis_etc: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_etc.stamp
build applications/bsp_app/flex_diagnosis$:flex_diagnosis_group: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_group.stamp
build applications/bsp_app/flex_diagnosis$:flex_diagnosis_tools: phony obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_tools.stamp
build applications/bsp_app/flexidag_factory$:flexidag_factory: phony base/bin/flexidag_factory
build applications/bsp_app/flexidag_factory: phony base/bin/flexidag_factory
build applications/bsp_app/flexidag_factory$:flexidag_factory_etc: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_etc.stamp
build applications/bsp_app/flexidag_factory$:flexidag_factory_group: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_group.stamp
build applications/bsp_app/flexidag_factory$:flexidag_factory_shell: phony obj/applications/bsp_app/flexidag_factory/flexidag_factory_shell.stamp
build applications/bsp_app/flexidag_factory$:get_mcu_version_shell: phony obj/applications/bsp_app/flexidag_factory/get_mcu_version_shell.stamp
build applications/bsp_app/flexidag_factory$:isotp_tester: phony base/flexidag_factory/isotp_tester
build applications/bsp_app/libdecode$:libdecode_group: phony obj/applications/bsp_app/libdecode/libdecode_group.stamp
build applications/bsp_app/libdecode$:librtosdecmjpegV5: phony ./librtosdecmjpegV5.so
build applications/bsp_app/ota$:libabinstaller_static: phony obj/applications/bsp_app/ota/libabinstaller_static.stamp
build applications/bsp_app/ota$:libabinstallerpl_static: phony obj/applications/bsp_app/ota/libabinstallerpl_static.stamp
build applications/bsp_app/ota$:libupgrade_api: phony ./libupgrade_api.so
build applications/bsp_app/ota$:mi_ota_tool: phony bin/mi_ota_tool
build applications/bsp_app/ota$:ota_burn: phony bin/ota_burn
build applications/bsp_app/ota$:upgrade_group: phony obj/applications/bsp_app/ota/upgrade_group.stamp
build applications/bsp_app/sync_status$:sync_status: phony misc/tools/sync_status
build applications/bsp_app/sync_status: phony misc/tools/sync_status
build applications/bsp_app/sys_perf_daemon$:gpu_performance_dump: phony obj/applications/bsp_app/sys_perf_daemon/gpu_performance_dump.stamp
build applications/bsp_app/sys_perf_daemon$:res_protobuf: phony obj/applications/bsp_app/sys_perf_daemon/res_protobuf.stamp
build applications/bsp_app/sys_perf_daemon$:sys_perf_daemon: phony bin/sys_perf_daemon
build applications/bsp_app/sys_perf_daemon: phony bin/sys_perf_daemon
build applications/bsp_app/sys_perf_daemon$:sys_perf_daemon_group: phony obj/applications/bsp_app/sys_perf_daemon/sys_perf_daemon_group.stamp
build applications/bsp_app/timesync$:gnss_sync_to_mcu_phc: phony obj/applications/bsp_app/timesync/gnss_sync_to_mcu_phc.stamp
build applications/bsp_app/timesync$:mcu_phc_sync_to_system: phony obj/applications/bsp_app/timesync/mcu_phc_sync_to_system.stamp
build applications/bsp_app/timesync$:rtc_sync_to_phc0: phony obj/applications/bsp_app/timesync/rtc_sync_to_phc0.stamp
build applications/bsp_app/timesync$:timesync: phony base/timesync/bin/timesync
build applications/bsp_app/timesync: phony base/timesync/bin/timesync
build applications/bsp_app/timesync$:timesync_config_etc: phony obj/applications/bsp_app/timesync/timesync_config_etc.stamp
build applications/bsp_app/timesync$:timesync_etc: phony obj/applications/bsp_app/timesync/timesync_etc.stamp
build applications/bsp_app/timesync$:timesync_group: phony obj/applications/bsp_app/timesync/timesync_group.stamp
build applications/bsp_app/timesync$:timesync_test_sh: phony obj/applications/bsp_app/timesync/timesync_test_sh.stamp
build build/minieye$:all: phony obj/build/minieye/all.stamp
build middleware/communication/libevutil$:libevutil: phony ./libevutil.so
build middleware/communication/libevutil: phony ./libevutil.so
build middleware/communication/libnnflow$:nnflow: phony ./libnnflow.so
build middleware/communication/libnnflow$:nnflow_group: phony obj/middleware/communication/libnnflow/nnflow_group.stamp
build middleware/communication/libnnflow$:nnpubsub: phony base/nnflow/nnpubsub
build middleware/communication/libnnflow$:nnreqrep: phony base/nnflow/nnreqrep
build middleware/communication/libnnflow$:sample_nnflow: phony base/nnflow/sample_nnflow
build middleware/communication/nanomsg$:PairTest: phony bin/PairTest
build middleware/communication/nanomsg$:PubSubTest: phony bin/PubSubTest
build middleware/communication/nanomsg$:ReqRepTest: phony bin/ReqRepTest
build middleware/communication/nanomsg$:SurveyTest: phony bin/SurveyTest
build middleware/communication/nanomsg$:libnnmsg: phony ./libnnmsg.so
build middleware/communication/nanomsg$:library_nanomsg_group: phony obj/middleware/communication/nanomsg/library_nanomsg_group.stamp
build middleware/daemon$:daemon_group: phony obj/middleware/daemon/daemon_group.stamp
build middleware/daemon/sample$:daemon_sample: phony obj/middleware/daemon/sample/daemon_sample.stamp
build middleware/daemon/sample$:sample_em: phony bin/sample_em
build middleware/daemon/src/em$:daemon: phony ./libdaemon.so
build middleware/daemon/src/property$:property: phony ./libproperty.so
build middleware/daemon/src/property: phony ./libproperty.so
build middleware/daemon/src/server$:daemon: phony bin/daemon
build middleware/daemon/utils$:dumprc: phony bin/dumprc
build middleware/daemon/utils$:getsystemprop: phony bin/getsystemprop
build middleware/daemon/utils$:setsystemprop: phony bin/setsystemprop
build middleware/daemon/utils$:startrc: phony bin/startrc
build middleware/daemon/utils$:stoprc: phony bin/stoprc
build middleware/daemon/utils$:utils: phony obj/middleware/daemon/utils/utils.stamp
build middleware/daemon/utils: phony obj/middleware/daemon/utils/utils.stamp
build middleware/dumpsys$:dumpsys: phony bin/dumpsys
build middleware/dumpsys: phony bin/dumpsys
build middleware/dumpsys$:dumpsys_group: phony obj/middleware/dumpsys/dumpsys_group.stamp
build middleware/dumpsys$:libdumpsys_interface: phony ./libdumpsys_interface.so
build middleware/dumpsys$:sample_dumpsys: phony bin/sample_dumpsys
build middleware/ets_service$:ets_service: phony base/ets_service/bin/ets_service
build middleware/ets_service: phony base/ets_service/bin/ets_service
build middleware/ets_service$:ets_service_etc: phony obj/middleware/ets_service/ets_service_etc.stamp
build middleware/ets_service$:ets_service_group: phony obj/middleware/ets_service/ets_service_group.stamp
build middleware/ets_service$:ets_service_run: phony obj/middleware/ets_service/ets_service_run.stamp
build middleware/logd$:logd_group: phony obj/middleware/logd/logd_group.stamp
build middleware/logd/sample$:sample_logd: phony bin/sample_logd
build middleware/logd/src/base$:base_logd: phony ./libbase_logd.a
build middleware/logd/src/libcutils$:libcutils_logd: phony ./libcutils_logd.a
build middleware/logd/src/liblog$:liblogd: phony ./liblogd.so
build middleware/logd/src/liblog$:liblogd_static: phony ./liblogd_static.a
build middleware/logd/src/libpackagelistparser$:libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build middleware/logd/src/libsysutils$:libsysutils_logd: phony ./libsysutils_logd.a
build middleware/logd/src/logcat$:logdcat: phony bin/logdcat
build middleware/logd/src/logd$:logd: phony bin/logd
build middleware/logd/src/logd: phony bin/logd
build middleware/logd/src/pcre$:libpcre_logd: phony ./libpcre_logd.a
build middleware/logd/src/pcre$:libpcrecpp_logd: phony ./libpcrecpp_logd.a
build middleware/mlog$:libmlog: phony ./libmlog.so
build middleware/mlog$:libmlog_static: phony ./libmlog_static.a
build middleware/mlog$:mlog_group: phony obj/middleware/mlog/mlog_group.stamp
build middleware/mlog$:mlogcat: phony bin/mlogcat
build middleware/mlog/sample$:mlog_sample: phony obj/middleware/mlog/sample/mlog_sample.stamp
build middleware/mlog/sample$:mlogsend: phony bin/mlogsend
build middleware/mlog/sample$:sample_mlog: phony bin/sample_mlog
build middleware/persistency$:libpersistency: phony ./libpersistency.so
build middleware/persistency$:libpersistency_common: phony ./libpersistency_common.so
build middleware/persistency$:persistency: phony bin/persistency
build middleware/persistency: phony bin/persistency
build middleware/persistency$:persistency_group: phony obj/middleware/persistency/persistency_group.stamp
build middleware/persistency/utils$:getshareconfig: phony bin/getshareconfig
build middleware/persistency/utils$:setshareconfig: phony bin/setshareconfig
build middleware/persistency/utils$:utils: phony obj/middleware/persistency/utils/utils.stamp
build middleware/persistency/utils: phony obj/middleware/persistency/utils/utils.stamp
build middleware/someip$:symlink_vsomeip3: phony obj/middleware/someip/symlink_vsomeip3.stamp
build middleware/someip$:symlink_vsomeip3_cfg: phony obj/middleware/someip/symlink_vsomeip3_cfg.stamp
build middleware/someip$:symlink_vsomeip3_e2e: phony obj/middleware/someip/symlink_vsomeip3_e2e.stamp
build middleware/someip$:symlink_vsomeip3_sd: phony obj/middleware/someip/symlink_vsomeip3_sd.stamp
build middleware/someip$:vsomeip3: phony ./libvsomeip3.so
build middleware/someip$:vsomeip3-cfg: phony ./libvsomeip3-cfg.so
build middleware/someip$:vsomeip3-e2e: phony ./libvsomeip3-e2e.so
build middleware/someip$:vsomeip3-sd: phony ./libvsomeip3-sd.so
build middleware/someip$:vsomeip3_group: phony obj/middleware/someip/vsomeip3_group.stamp
build middleware/system/core/filelog$:filelog_group: phony obj/middleware/system/core/filelog/filelog_group.stamp
build middleware/system/core/filelog$:libfilelog: phony ./libfilelog.so
build middleware/system/core/filelog/demo$:demo_filelog: phony ./demo_filelog
build middleware/system/core/filelog/demo$:filelog_demo: phony obj/middleware/system/core/filelog/demo/filelog_demo.stamp
build middleware/system/core/libcppbase$:cppbase: phony ./libcppbase.so
build middleware/system/core/libjsonUtil$:jsonUtil: phony ./libjsonUtil.so
build middleware/system/core/libjsonUtil$:jsonUtil_group: phony obj/middleware/system/core/libjsonUtil/jsonUtil_group.stamp
build middleware/system/core/libjsonUtil$:sample_json_test: phony bin/sample_json_test
build middleware/system/core/libmessage$:message: phony ./libmessage.so
build middleware/system/core/libmessage/demo$:sample_message: phony bin/sample_message
build middleware/system/tools/filemonitor$:file_monitor: phony bin/file_monitor
build middleware/system/tools/log_global_save$:archive-log: phony obj/middleware/system/tools/log_global_save/archive-log.stamp
build middleware/system/tools/log_global_save$:log_tools: phony obj/middleware/system/tools/log_global_save/log_tools.stamp
build middleware/system/tools/log_global_save$:minieye-log: phony obj/middleware/system/tools/log_global_save/minieye-log.stamp
build middleware/system/tools/log_to_libflow$:log_to_dds: phony bin/log_to_dds
build middleware/system/tools/log_to_libflow$:log_to_libflow: phony bin/log_to_libflow
build middleware/system/tools/log_to_libflow: phony bin/log_to_libflow
build middleware/system/tools/scantree$:scantree: phony bin/scantree
build middleware/system/tools/scantree: phony bin/scantree
build middleware/system/tools/system_res_monitor$:res_protobuf: phony obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp
build middleware/system/tools/system_res_monitor$:system_res_monitor: phony bin/system_res_monitor
build middleware/system/tools/system_res_monitor: phony bin/system_res_monitor
build middleware/tombstone$:tombstone_group: phony obj/middleware/tombstone/tombstone_group.stamp
build middleware/tombstone/sample$:sample_tombstone: phony bin/sample_tombstone
build middleware/tombstone/src/backtrace$:backtrace_tombstone: phony ./libbacktrace_tombstone.a
build middleware/tombstone/src/backtrace/demangle$:demangle_tombstone: phony ./libdemangle_tombstone.a
build middleware/tombstone/src/base$:base_tombstone: phony ./libbase_tombstone.a
build middleware/tombstone/src/debuggerd$:test: phony bin/test
build middleware/tombstone/src/debuggerd$:tombstone: phony bin/tombstone
build middleware/tombstone/src/debuggerd$:tombstone_client: phony ./libtombstone_client.so
build middleware/tombstone/src/libcutils$:libcutils_tombstone: phony ./libcutils_tombstone.a
build middleware/tombstone/src/procinfo$:procinfo_tombstone: phony ./libprocinfo_tombstone.a
build middleware/tombstone/src/unwindstack$:unwindstack_tombstone: phony ./libunwindstack_tombstone.a
build middleware/tombstone/src/unwindstack/lzma$:lzma_tombstone: phony ./liblzma_tombstone.a
build middleware/tombstone/utils$:backtrace_tool: phony bin/backtrace_tool
build middleware/upper_tester$:upper_tester: phony base/upper_tester/bin/upper_tester
build middleware/upper_tester: phony base/upper_tester/bin/upper_tester
build middleware/upper_tester$:upper_tester_group: phony obj/middleware/upper_tester/upper_tester_group.stamp
build middleware/upper_tester$:ut_run: phony obj/middleware/upper_tester/ut_run.stamp

build all: phony $
    base/caninput/bin/TestDataDds $
    base/caninput/bin/TestDataIpc $
    base/caninput/bin/TestVehSignalHandler $
    base/caninput/bin/caninput $
    obj/applications/app/caninput/caninput_etc.stamp $
    obj/applications/app/caninput/caninput_group.stamp $
    obj/applications/app/caninput/caninput_run.stamp $
    obj/applications/app/caninput/caninput_test_run.stamp $
    base/caninput/bin/ddsreader_test_caninput $
    base/caninput/bin/module_test $
    base/canout/bin/canout $
    obj/applications/app/canout/canout_etc.stamp $
    obj/applications/app/canout/canout_group.stamp $
    obj/applications/app/canout/canout_run.stamp $
    obj/applications/app/canout/canout_test_run.stamp $
    base/canout/bin/ddsreader_test $
    base/canout/bin/ddswriter_test $
    base/canout/bin/module_test $
    obj/applications/app/canout/test/sample_json.stamp $
    obj/applications/app/canout/test/test.stamp $
    base/canout/bin/test_dds_reader $
    base/canout/bin/test_dds_writer $
    obj/applications/app/common/common_group.stamp $
    bin/CanIoTest $
    bin/canHaldump $
    ./libcanio.so $
    obj/applications/app/common/canio/library_canio_group.stamp $
    ./libdbc.so $
    obj/applications/app/common/dbc/libdbc_group.stamp $
    bin/RMAgentTest $
    bin/get_version $
    ./libRMAgent.so $
    obj/applications/app/common/libRMAgent/libRMAgent_group.stamp $
    ./libSigVerify.so $
    ./libUniComm.so $
    ./libcollect.so $
    ./libdata_proto_o.so $
    obj/applications/app/common/pb/libproto_group.stamp $
    ./libminieyetimehal.so $
    obj/applications/app/common/timehal/libminieyetimehal_group.stamp $
    bin/sample_timehal $
    ./libcommon_utils.so $
    obj/applications/app/common/utils/libcommon_utils_group.stamp $
    ./libdiagnosis.so $
    obj/applications/app/diagnosis/diagnosis_group.stamp $
    obj/applications/app/diagnosis/diagnostic_etc.stamp $
    base/diagnostic/bin/fault_server $
    base/diagnostic/bin/report_fault $
    obj/applications/app/diagnosis/shell.stamp $
    base/diagnostic/bin/unittest_diagnosis $
    base/doip/bin/broadcast_test $
    base/doip/bin/doip $
    ./libdoipNnSrv.so $
    obj/applications/app/doip/doip_config.stamp $
    obj/applications/app/doip/doip_group.stamp $
    obj/applications/app/doip/doip_sh.stamp $
    bin/get_dtc $
    base/doip/bin/nnTest $
    ./libDoIP.so $
    obj/applications/app/doip/src/libDoIP/libDoIP_group.stamp $
    ./libUDS.so $
    obj/applications/app/doip/src/libUDS/libUDS_group.stamp $
    ./libUdsOnDoIP.so $
    obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP_group.stamp $
    base/gnss/bin/GnssRawReader $
    base/gnss/bin/gnss $
    obj/applications/app/gnss/gnss_group.stamp $
    libs/librtklib.a $
    base/gnss/bin/modtool $
    obj/applications/app/gnss/shell.stamp $
    obj/applications/app/gnss/shell1.stamp $
    obj/applications/app/gnss/shell2.stamp $
    obj/applications/app/gnss/shell3.stamp $
    obj/applications/app/gnss/this_conf_dir.stamp $
    base/idvr/bin/idvr $
    obj/applications/app/idvr/idvr_group.stamp $
    obj/applications/app/idvr/shell.stamp $
    obj/applications/app/idvr/this_conf_dir.stamp $
    base/imu/bin/ImuClientTest $
    base/imu/bin/ddsreader_imu $
    base/imu/bin/ddswriter_imu $
    obj/applications/app/imu/dv_run.stamp $
    base/imu/bin/dv_test $
    base/imu/bin/imu_app $
    obj/applications/app/imu/imu_etc.stamp $
    obj/applications/app/imu/imu_group.stamp $
    base/imu/bin/imu_recv_demo $
    obj/applications/app/imu/imu_run.stamp $
    obj/applications/app/imu/imu_test_run.stamp $
    ./libImuClient.so $
    base/imu/bin/module_test $
    base/imu/bin/unittest $
    base/imu/bin/imu_ota $
    base/imu/imu_sim/bin/imu_sim $
    obj/applications/app/imu/utils/imu_sim_etc.stamp $
    obj/applications/app/imu/utils/imu_sim_readme.stamp $
    obj/applications/app/imu/utils/imu_sim_run.stamp $
    obj/applications/app/imu/utils/ota_lib.stamp $
    obj/applications/app/imu/utils/ota_run.stamp $
    base/imu/imu_sim/bin/sim_dds_test $
    obj/applications/app/imu/utils/utils.stamp $
    ./libPhmAgent.so $
    bin/gtest $
    base/phm/bin/phm $
    bin/phmAgtSample $
    obj/applications/app/phm/phm_cfg_dir.stamp $
    obj/applications/app/phm/phm_group.stamp $
    obj/applications/app/phm/run_sh.stamp $
    base/qxids_sdk/bin/ddsreader_test $
    base/qxids_sdk/bin/ddswriter_test $
    base/qxids_sdk/bin/module_test $
    obj/applications/app/qxids_sdk/nssr_run.stamp $
    base/qxids_sdk/bin/ppp_engine_app $
    base/qxids_sdk/bin/ppp_engine_app_bak $
    obj/applications/app/qxids_sdk/pppe_run.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_etc.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_group.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_libs.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_run.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_test_run.stamp $
    obj/applications/app/qxids_sdk/qxids_sdk_test_sh.stamp $
    obj/applications/app/qxids_sdk/sdk_version_file.stamp $
    base/qxids_sdk/bin/service_nssr $
    base/radar/radar $
    obj/applications/app/radar/radar_etc.stamp $
    obj/applications/app/radar/radar_group.stamp $
    obj/applications/app/radar/radar_run.stamp $
    obj/applications/app/radar/radar_test_sh.stamp $
    base/radar/UintTest $
    base/radar/client_radar $
    base/sr_hmi_client/bin/JetouSdMapTest $
    base/sr_hmi_client/bin/sr_hmi_client $
    obj/applications/app/sr_hmi_client/sr_hmi_client_cfg.stamp $
    obj/applications/app/sr_hmi_client/sr_hmi_client_group.stamp $
    obj/applications/app/sr_hmi_client/sr_hmi_client_run.stamp $
    base/sr_hmi_client/bin/tboxMsgTest $
    ./libSrDataSdk.so $
    ./libsensor_status_plugin.so $
    obj/applications/app/sr_hmi_service/routing_run.stamp $
    base/sr_hmi_service/bin/someip_routing $
    base/sr_hmi_service/bin/sr_client_simulation $
    base/sr_hmi_service/bin/sr_hmi_service $
    obj/applications/app/sr_hmi_service/sr_hmi_service_cfg.stamp $
    obj/applications/app/sr_hmi_service/sr_hmi_service_run.stamp $
    base/sr_hmi_service/bin/sr_input_test $
    obj/applications/app/sr_hmi_service/sr_service_group.stamp $
    base/sr_hmi_service/bin/AdasHmiTest $
    obj/applications/app/sr_hmi_service/sample/sr_adashmi_run.stamp $
    obj/applications/app/sr_hmi_service/sample/sr_sample_group.stamp $
    ./libapa_plugin_example.so $
    ./libsr_core_manager.so $
    ./libvehicle_plugin_example.so $
    base/sr_hmi_service/bin/plugin_service $
    obj/applications/app/sr_hmi_service/src/plugin/sr_hmi_plugin_cfg.stamp $
    obj/applications/app/sr_hmi_service/src/plugin/sr_plugin_group.stamp $
    ./libStateAgent.so $
    ./libStateTrigger.so $
    base/state_manager/bin/state_change_test $
    base/state_manager/bin/state_client_test $
    base/state_manager/bin/state_manager $
    obj/applications/app/state_manager/state_manager_etc.stamp $
    obj/applications/app/state_manager/state_manager_group.stamp $
    obj/applications/app/state_manager/state_manager_run.stamp $
    base/state_manager/bin/classtest_state $
    obj/applications/app/state_manager/test/state_test.stamp $
    obj/applications/app/state_manager/test/stresstest_run.stamp $
    base/state_manager/bin/unittest_state $
    base/bin/mcap_reader $
    base/takepoint_collect/bin/takepoint_collect $
    obj/applications/app/takepoint_collect/takepoint_collect_config.stamp $
    obj/applications/app/takepoint_collect/takepoint_collect_group.stamp $
    obj/applications/app/takepoint_collect/takepoint_collect_sh.stamp $
    base/bin/takepoint_collect_tools_decrypt $
    obj/applications/app/takepoint_collect/takepoint_collect_util.stamp $
    obj/applications/app/takepoint_collect/takepoint_thirdparty.stamp $
    base/takepoint_test/bin/takepoint_test $
    obj/applications/app/takepoint_test/takepoint_test_group.stamp $
    obj/applications/app/takepoint_test/takepoint_test_sh.stamp $
    obj/applications/app/test/test_group.stamp $
    obj/applications/app/test/dds_gtest/dds_gtest_group.stamp $
    base/test/dds_test/bin/dds_unittest $
    base/vout/bin/app_avm_out $
    obj/applications/bsp_app/app_avm_out/app_avm_out_group.stamp $
    obj/applications/bsp_app/app_avm_out/vout_config.stamp $
    obj/applications/bsp_app/app_avm_out/vout_run.stamp $
    base/vout/bin/voutdiagnose $
    base/stitch/bin/avm_pym_stitch $
    obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_conf.stamp $
    obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_group.stamp $
    obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_run.stamp $
    base/camera/bin/UintTest $
    base/camera/bin/camera_client $
    obj/applications/bsp_app/camera_service/camera_run.stamp $
    base/camera/bin/camera_service $
    obj/applications/bsp_app/camera_service/camera_service_conf.stamp $
    obj/applications/bsp_app/camera_service/camera_service_group.stamp $
    obj/applications/bsp_app/camera_service/camera_test.stamp $
    obj/applications/bsp_app/camera_service/encode_run.stamp $
    ./libCameraClient.so $
    obj/applications/bsp_app/camera_service/run_camera_adas.stamp $
    obj/applications/bsp_app/camera_service/run_camera_apa.stamp $
    base/camera/bin/camera_dds_recv $
    base/camera/bin/camera_tool $
    base/camera/bin/getcameraimage $
    obj/applications/bsp_app/can_utils/can_utils_group.stamp $
    bin/candump $
    bin/cansend $
    base/camera/bin/camera_encode_libflow $
    obj/applications/bsp_app/encode_libflow/camera_encode_libflow_group.stamp $
    base/eth_diagnosis/bin/eth_diagnosis $
    obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_etc.stamp $
    obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_group.stamp $
    base/eth_diagnosis/bin/eth_diagnosis_subscript $
    base/flex_diagnosis/can_view $
    base/flex_diagnosis/flex_diagnosis $
    obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_etc.stamp $
    obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_group.stamp $
    obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_tools.stamp $
    base/bin/flexidag_factory $
    obj/applications/bsp_app/flexidag_factory/flexidag_factory_etc.stamp $
    obj/applications/bsp_app/flexidag_factory/flexidag_factory_group.stamp $
    obj/applications/bsp_app/flexidag_factory/flexidag_factory_shell.stamp $
    obj/applications/bsp_app/flexidag_factory/get_mcu_version_shell.stamp $
    base/flexidag_factory/isotp_tester $
    obj/applications/bsp_app/libdecode/libdecode_group.stamp $
    ./librtosdecmjpegV5.so $
    obj/applications/bsp_app/ota/libabinstaller_static.stamp $
    obj/applications/bsp_app/ota/libabinstallerpl_static.stamp $
    ./libupgrade_api.so $
    bin/mi_ota_tool $
    bin/ota_burn $
    obj/applications/bsp_app/ota/upgrade_group.stamp $
    misc/tools/sync_status $
    obj/applications/bsp_app/sys_perf_daemon/gpu_performance_dump.stamp $
    obj/applications/bsp_app/sys_perf_daemon/res_protobuf.stamp $
    bin/sys_perf_daemon $
    obj/applications/bsp_app/sys_perf_daemon/sys_perf_daemon_group.stamp $
    obj/applications/bsp_app/timesync/gnss_sync_to_mcu_phc.stamp $
    obj/applications/bsp_app/timesync/mcu_phc_sync_to_system.stamp $
    obj/applications/bsp_app/timesync/rtc_sync_to_phc0.stamp $
    base/timesync/bin/timesync $
    obj/applications/bsp_app/timesync/timesync_config_etc.stamp $
    obj/applications/bsp_app/timesync/timesync_etc.stamp $
    obj/applications/bsp_app/timesync/timesync_group.stamp $
    obj/applications/bsp_app/timesync/timesync_test_sh.stamp $
    obj/build/minieye/all.stamp $
    ./libevutil.so $
    ./libnnflow.so $
    obj/middleware/communication/libnnflow/nnflow_group.stamp $
    base/nnflow/nnpubsub $
    base/nnflow/nnreqrep $
    base/nnflow/sample_nnflow $
    bin/PairTest $
    bin/PubSubTest $
    bin/ReqRepTest $
    bin/SurveyTest $
    ./libnnmsg.so $
    obj/middleware/communication/nanomsg/library_nanomsg_group.stamp $
    obj/middleware/daemon/daemon_group.stamp $
    obj/middleware/daemon/sample/daemon_sample.stamp $
    bin/sample_em $
    ./libdaemon.so $
    ./libproperty.so $
    bin/daemon $
    bin/dumprc $
    bin/getsystemprop $
    bin/setsystemprop $
    bin/startrc $
    bin/stoprc $
    obj/middleware/daemon/utils/utils.stamp $
    bin/dumpsys $
    obj/middleware/dumpsys/dumpsys_group.stamp $
    ./libdumpsys_interface.so $
    bin/sample_dumpsys $
    base/ets_service/bin/ets_service $
    obj/middleware/ets_service/ets_service_etc.stamp $
    obj/middleware/ets_service/ets_service_group.stamp $
    obj/middleware/ets_service/ets_service_run.stamp $
    obj/middleware/logd/logd_group.stamp $
    bin/sample_logd $
    ./libbase_logd.a $
    ./libcutils_logd.a $
    ./liblogd.so $
    ./liblogd_static.a $
    ./libpackagelistparser_logd.a $
    ./libsysutils_logd.a $
    bin/logdcat $
    bin/logd $
    ./libpcre_logd.a $
    ./libpcrecpp_logd.a $
    ./libmlog.so $
    ./libmlog_static.a $
    obj/middleware/mlog/mlog_group.stamp $
    bin/mlogcat $
    obj/middleware/mlog/sample/mlog_sample.stamp $
    bin/mlogsend $
    bin/sample_mlog $
    ./libpersistency.so $
    ./libpersistency_common.so $
    bin/persistency $
    obj/middleware/persistency/persistency_group.stamp $
    bin/getshareconfig $
    bin/setshareconfig $
    obj/middleware/persistency/utils/utils.stamp $
    obj/middleware/someip/symlink_vsomeip3.stamp $
    obj/middleware/someip/symlink_vsomeip3_cfg.stamp $
    obj/middleware/someip/symlink_vsomeip3_e2e.stamp $
    obj/middleware/someip/symlink_vsomeip3_sd.stamp $
    ./libvsomeip3.so $
    ./libvsomeip3-cfg.so $
    ./libvsomeip3-e2e.so $
    ./libvsomeip3-sd.so $
    obj/middleware/someip/vsomeip3_group.stamp $
    obj/middleware/system/core/filelog/filelog_group.stamp $
    ./libfilelog.so $
    ./demo_filelog $
    obj/middleware/system/core/filelog/demo/filelog_demo.stamp $
    ./libcppbase.so $
    ./libjsonUtil.so $
    obj/middleware/system/core/libjsonUtil/jsonUtil_group.stamp $
    bin/sample_json_test $
    ./libmessage.so $
    bin/sample_message $
    bin/file_monitor $
    obj/middleware/system/tools/log_global_save/archive-log.stamp $
    obj/middleware/system/tools/log_global_save/log_tools.stamp $
    obj/middleware/system/tools/log_global_save/minieye-log.stamp $
    bin/log_to_dds $
    bin/log_to_libflow $
    bin/scantree $
    obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp $
    bin/system_res_monitor $
    obj/middleware/tombstone/tombstone_group.stamp $
    bin/sample_tombstone $
    ./libbacktrace_tombstone.a $
    ./libdemangle_tombstone.a $
    ./libbase_tombstone.a $
    bin/test $
    bin/tombstone $
    ./libtombstone_client.so $
    ./libcutils_tombstone.a $
    ./libprocinfo_tombstone.a $
    ./libunwindstack_tombstone.a $
    ./liblzma_tombstone.a $
    bin/backtrace_tool $
    base/upper_tester/bin/upper_tester $
    obj/middleware/upper_tester/upper_tester_group.stamp $
    obj/middleware/upper_tester/ut_run.stamp

default all
