defines = -DMLOG_WITH_LOGD -DMLOG_WITH_FILE_MINIEYE
include_dirs = -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
target_output_name = libmlog_static

build obj/middleware/mlog/src/libmlog_static.MiniLog.o: cxx ../../middleware/mlog/src/MiniLog.cpp
build obj/middleware/mlog/src/libmlog_static.SocketServer.o: cxx ../../middleware/mlog/src/SocketServer.cpp

build ./libmlog_static.a: alink obj/middleware/mlog/src/libmlog_static.MiniLog.o obj/middleware/mlog/src/libmlog_static.SocketServer.o || ./liblogd_static.a
  arflags =
  output_extension = .a
  output_dir = .
