defines =
include_dirs = -I../../middleware/mlog/src -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
target_output_name = mlogcat

build obj/middleware/mlog/utils/mlogcat.mlogcat.o: cxx ../../middleware/mlog/utils/mlogcat.cpp
build obj/middleware/mlog/src/mlogcat.SocketClient.o: cxx ../../middleware/mlog/src/SocketClient.cpp

build bin/mlogcat: link obj/middleware/mlog/utils/mlogcat.mlogcat.o obj/middleware/mlog/src/mlogcat.SocketClient.o
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib
  libs =
  output_extension = 
  output_dir = bin
