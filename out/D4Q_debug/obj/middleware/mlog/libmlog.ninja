defines = -DMLOG_WITH_LOGD -DMLOG_WITH_FILE_MINIEYE
include_dirs = -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIC
target_output_name = libmlog

build obj/middleware/mlog/src/libmlog.MiniLog.o: cxx ../../middleware/mlog/src/MiniLog.cpp
build obj/middleware/mlog/src/libmlog.SocketServer.o: cxx ../../middleware/mlog/src/SocketServer.cpp

build ./libmlog.so: solink obj/middleware/mlog/src/libmlog.MiniLog.o obj/middleware/mlog/src/libmlog.SocketServer.o ./liblogd.so
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
