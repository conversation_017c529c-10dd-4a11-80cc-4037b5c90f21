defines = -D__PROTOCOL_VERCODE__=12 -D__AB_SIZEOF_POINTER__=8 -DLINUX -DVERSION=\"3.5.0\" -DUSE_DEBUG
include_dirs = -I../../applications/bsp_app/ota/src/ota_burn/adaptor/include -I../../applications/bsp_app/ota/src/ota_burn/agent/include -I../../applications/bsp_app/ota/src/depends/hbre/include -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../applications/bsp_app/ota/src/depends/installer/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_c =
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
target_output_name = ota_burn

build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_common.o: cxx ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_common.cpp || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_file.o: cc ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_file.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_fsmgr.o: cc ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_fsmgr.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_log.o: cc ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_log.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_mem.o: cc ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_mem.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_simple_getline.o: cc ../../applications/bsp_app/ota/src/ota_burn/adaptor/src/abota_pl_simple_getline.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.cJSON.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/cJSON.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.dev_data.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/dev_data.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.dlsym.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/dlsym.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.ota_burn.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.utils.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/utils.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.main.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/main.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.Mlog.o: cxx ../../applications/bsp_app/ota/src/ota_burn/agent/src/Mlog.cpp || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.sha256-stream.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/sha256-stream.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp
build obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.sha256.o: cc ../../applications/bsp_app/ota/src/ota_burn/agent/src/sha256.c || obj/applications/bsp_app/ota/libabinstaller_static.stamp

build bin/ota_burn: link obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_common.o obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_file.o obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_fsmgr.o obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_log.o obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_mem.o obj/applications/bsp_app/ota/src/ota_burn/adaptor/src/ota_burn.abota_pl_simple_getline.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.cJSON.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.dev_data.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.dlsym.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.ota_burn.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.utils.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.main.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.Mlog.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.sha256-stream.o obj/applications/bsp_app/ota/src/ota_burn/agent/src/ota_burn.sha256.o ./libmlog_static.a ./liblogd_static.a | libs/libabinstaller_static.a || obj/applications/bsp_app/ota/libabinstaller_static.stamp
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib
  libs = libs/libabinstaller_static.a
  output_extension = 
  output_dir = bin
