defines =
include_dirs = -I../../platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc/usr/minieye/include -I../../platform/D4Q/build/minieye_sdk/include/camera -I../../middleware/dumpsys/include -I../../applications/app/takepoint_test/src -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include -I../../middleware/mlog/include -I../../middleware/system/core/libjsonUtil/include -I../../middleware/communication/libevutil/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../platform/D4Q/build/sysroots/usr/hobot/include/aarch64-linux-gnu -I../../middleware/tombstone/include -I../../applications/app/common/libRMAgent/include -I../../applications/app/common/libRMAgent/src -I../../applications/app/common/pb/generate/include -I../../applications/app/common/pb/generate/include/parking -I../../applications/app/common/pb/temp -I../../applications/app/state_manager/include -I../../applications/app/state_manager/include/StateAgent/client -I../../applications/app/state_manager/src/stateman/notify -I../../applications/app/state_manager/src/stateman/request -I../../applications/app/state_manager/src/stateman/common -I../../applications/app/state_manager/src/libStateAgent
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
target_output_name = takepoint_test

build obj/applications/app/takepoint_test/src/takepoint_test.DdsParserMnger.o: cxx ../../applications/app/takepoint_test/src/DdsParserMnger.cpp
build obj/applications/app/takepoint_test/src/takepoint_test.RecentDataCache.o: cxx ../../applications/app/takepoint_test/src/RecentDataCache.cpp
build obj/applications/app/takepoint_test/src/takepoint_test.main.o: cxx ../../applications/app/takepoint_test/src/main.cpp

build base/takepoint_test/bin/takepoint_test: link obj/applications/app/takepoint_test/src/takepoint_test.DdsParserMnger.o obj/applications/app/takepoint_test/src/takepoint_test.RecentDataCache.o obj/applications/app/takepoint_test/src/takepoint_test.main.o ./libcppbase.so ./libmlog.so ./libjsonUtil.so ./libevutil.so ./libtombstone_client.so ./libRMAgent.so ./libdata_proto_o.so ./libStateAgent.so ./libdumpsys_interface.so ./libnnmsg.so || obj/middleware/dumpsys/dumpsys_group.stamp
  ldflags = -ldds_api -ldds -lprotobuf -lz -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib/aarch64-linux-gnu -lev -lssl -lcrypto
  libs =
  output_extension = 
  output_dir = base/takepoint_test/bin
