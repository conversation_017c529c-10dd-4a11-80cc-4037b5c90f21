defines =
include_dirs = -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/dumpsys/include -I../../middleware/tombstone/include -I../../applications/app/common/pb/include -I../../applications/app/common/timehal/include -I../../applications/app/gnss/src/dataSource -I../../applications/app/gnss/src/gnss -I../../applications/app/gnss/src/tty -I../../applications/app/gnss/src/mgr -I../../applications/app/gnss/include -I../../applications/app/gnss/src -I../../applications/app/common/libRMAgent/include -I../../applications/app/common/libRMAgent/src -I../../applications/app/common/pb/generate/include -I../../applications/app/common/pb/generate/include/parking -I../../applications/app/common/pb/temp -I../../applications/app/diagnosis/include -I../../middleware/tombstone/include -I../../applications/app/common/pb/include -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE -Wall -Wno-deprecated-declarations
target_output_name = gnss

build obj/applications/app/gnss/src/dataSource/gnss.DataSource.o: cxx ../../applications/app/gnss/src/dataSource/DataSource.cpp
build obj/applications/app/gnss/src/gnss/gnss.GnssDataSource.o: cxx ../../applications/app/gnss/src/gnss/GnssDataSource.cpp
build obj/applications/app/gnss/src/gnss/gnss.GnssDev.o: cxx ../../applications/app/gnss/src/gnss/GnssDev.cpp
build obj/applications/app/gnss/src/mgr/gnss.DataSrcMgr.o: cxx ../../applications/app/gnss/src/mgr/DataSrcMgr.cpp
build obj/applications/app/gnss/src/tty/gnss.TtyDevice.o: cxx ../../applications/app/gnss/src/tty/TtyDevice.cpp
build obj/applications/app/gnss/src/gnss.main.o: cxx ../../applications/app/gnss/src/main.cpp

build base/gnss/bin/gnss: link obj/applications/app/gnss/src/dataSource/gnss.DataSource.o obj/applications/app/gnss/src/gnss/gnss.GnssDataSource.o obj/applications/app/gnss/src/gnss/gnss.GnssDev.o obj/applications/app/gnss/src/mgr/gnss.DataSrcMgr.o obj/applications/app/gnss/src/tty/gnss.TtyDevice.o obj/applications/app/gnss/src/gnss.main.o ./libRMAgent.so ./libdata_proto_o.so ./libdiagnosis.so ./libcppbase.so ./libmessage.so ./libminieyetimehal.so ./libmlog.so ./libmlog_static.a ./liblogd_static.a ./libdumpsys_interface.so ./libnnmsg.so ./libtombstone_client.so ./libfilelog.so || obj/applications/app/common/timehal/libminieyetimehal_group.stamp obj/middleware/mlog/mlog_group.stamp obj/middleware/dumpsys/dumpsys_group.stamp obj/middleware/tombstone/tombstone_group.stamp obj/middleware/system/core/filelog/filelog_group.stamp
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -ldds -ldds_api -ldlt -lprotobuf -lflow -lglog -lxml2 -lyaml-cpp -ljsoncpp -lfilelog -lnanomsg
  libs =
  output_extension = 
  output_dir = base/gnss/bin
