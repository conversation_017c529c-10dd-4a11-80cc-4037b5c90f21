defines =
include_dirs = -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../applications/app/common/pb/include -I../../applications/app/gnss/external/rtklib-2.4.3.b34+dfsg/src -I../../applications/app/gnss/src/dataSource -I../../applications/app/gnss/src/gnss -I../../applications/app/gnss/src/tty -I../../applications/app/gnss/src/mgr -I../../applications/app/gnss/include -I../../applications/app/gnss/src -I../../applications/app/common/pb/generate/include -I../../applications/app/common/pb/generate/include/parking -I../../applications/app/common/pb/temp -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE -Wall -Wno-deprecated-declarations
target_output_name = GnssRawReader

build obj/applications/app/gnss/src/tools/GnssRawReader.GnssRawDdsReader.o: cxx ../../applications/app/gnss/src/tools/GnssRawDdsReader.cpp

build base/gnss/bin/GnssRawReader: link obj/applications/app/gnss/src/tools/GnssRawReader.GnssRawDdsReader.o libs/librtklib.a ./libdata_proto_o.so ./libcppbase.so ./libmessage.so ./libmlog.so ./libmlog_static.a ./liblogd_static.a ./libfilelog.so || obj/middleware/mlog/mlog_group.stamp obj/middleware/system/core/filelog/filelog_group.stamp
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -ldds -ldds_api -ldlt -lprotobuf -lflow -lglog -lxml2 -lyaml-cpp -ljsoncpp -lfilelog /home/<USER>/project/d4q/out/D4Q_debug/libs/librtklib.a
  libs =
  output_extension = 
  output_dir = base/gnss/bin
