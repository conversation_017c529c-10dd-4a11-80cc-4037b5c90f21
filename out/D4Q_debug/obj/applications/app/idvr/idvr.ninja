defines =
include_dirs = -I../../platform/D4Q/build/sysroots/usr/minieye/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/tombstone/include -I../../platform/D4Q/build/sysroots/usr/hobot/include -I../../platform/D4Q/build/minieye_sdk/include/camera -I../../platform/D4Q/build/sysroots/usr/minieye/include/nlohmann -I../../applications/app/common/pb/generate/include -I../../applications/app/idvr/src/base -I../../applications/app/idvr/src/dds -I../../applications/app/idvr/src/media -I../../applications/app/idvr/src/media/venc -I../../applications/app/idvr/src/media/yuvdraw -I../../applications/app/idvr/src/rtsp/src/3rdpart -I../../applications/app/idvr/src/rtsp/src/net -I../../applications/app/idvr/src/rtsp/src/xop -I../../applications/app/idvr/src/rtsp/src -I../../applications/app/idvr/src/service -I../../applications/app/idvr/src -I../../applications/app/common/pb/generate/include -I../../applications/app/common/pb/generate/include/parking -I../../applications/app/common/pb/temp -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE
cflags_c =
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -fPIE -Wall -Wno-deprecated-declarations
target_output_name = idvr

build obj/applications/app/common/pb/generate/src/idvr.vehicle_signal.pb.o: cxx ../../applications/app/common/pb/generate/src/vehicle_signal.pb.cc
build obj/applications/app/common/pb/generate/src/idvr.camera.pb.o: cxx ../../applications/app/common/pb/generate/src/camera.pb.cc
build obj/applications/app/idvr/src/dds/idvr.VehicleSigDdsReader.o: cxx ../../applications/app/idvr/src/dds/VehicleSigDdsReader.cpp
build obj/applications/app/idvr/src/dds/idvr.CamDdsReader.o: cxx ../../applications/app/idvr/src/dds/CamDdsReader.cpp
build obj/applications/app/idvr/src/media/venc/idvr.Venc.o: cxx ../../applications/app/idvr/src/media/venc/Venc.cpp
build obj/applications/app/idvr/src/media/yuvdraw/idvr.mvgl_bmp.o: cc ../../applications/app/idvr/src/media/yuvdraw/mvgl_bmp.c
build obj/applications/app/idvr/src/media/yuvdraw/idvr.mvgl_util.o: cc ../../applications/app/idvr/src/media/yuvdraw/mvgl_util.c
build obj/applications/app/idvr/src/media/yuvdraw/idvr.YUVDraw.o: cxx ../../applications/app/idvr/src/media/yuvdraw/YUVDraw.cpp
build obj/applications/app/idvr/src/service/idvr.IdvrService.o: cxx ../../applications/app/idvr/src/service/IdvrService.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.Acceptor.o: cxx ../../applications/app/idvr/src/rtsp/src/net/Acceptor.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.BufferReader.o: cxx ../../applications/app/idvr/src/rtsp/src/net/BufferReader.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.BufferWriter.o: cxx ../../applications/app/idvr/src/rtsp/src/net/BufferWriter.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.EpollTaskScheduler.o: cxx ../../applications/app/idvr/src/rtsp/src/net/EpollTaskScheduler.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.EventLoop.o: cxx ../../applications/app/idvr/src/rtsp/src/net/EventLoop.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.Logger.o: cxx ../../applications/app/idvr/src/rtsp/src/net/Logger.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.MemoryManager.o: cxx ../../applications/app/idvr/src/rtsp/src/net/MemoryManager.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.NetInterface.o: cxx ../../applications/app/idvr/src/rtsp/src/net/NetInterface.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.Pipe.o: cxx ../../applications/app/idvr/src/rtsp/src/net/Pipe.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.SelectTaskScheduler.o: cxx ../../applications/app/idvr/src/rtsp/src/net/SelectTaskScheduler.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.SocketUtil.o: cxx ../../applications/app/idvr/src/rtsp/src/net/SocketUtil.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.TaskScheduler.o: cxx ../../applications/app/idvr/src/rtsp/src/net/TaskScheduler.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpConnection.o: cxx ../../applications/app/idvr/src/rtsp/src/net/TcpConnection.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpServer.o: cxx ../../applications/app/idvr/src/rtsp/src/net/TcpServer.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpSocket.o: cxx ../../applications/app/idvr/src/rtsp/src/net/TcpSocket.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.Timer.o: cxx ../../applications/app/idvr/src/rtsp/src/net/Timer.cpp
build obj/applications/app/idvr/src/rtsp/src/net/idvr.Timestamp.o: cxx ../../applications/app/idvr/src/rtsp/src/net/Timestamp.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.AACSource.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/AACSource.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.DigestAuthentication.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/DigestAuthentication.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.G711ASource.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/G711ASource.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.H264Parser.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/H264Parser.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.H264Source.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/H264Source.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.H265Source.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/H265Source.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.md5.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/md5.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.MediaSession.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/MediaSession.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtpConnection.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtpConnection.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspConnection.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtspConnection.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspMessage.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtspMessage.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspPuller.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtspPuller.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspPusher.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtspPusher.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspServer.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/RtspServer.cpp
build obj/applications/app/idvr/src/rtsp/src/xop/idvr.VP8Source.o: cxx ../../applications/app/idvr/src/rtsp/src/xop/VP8Source.cpp
build obj/applications/app/idvr/src/idvr.main.o: cxx ../../applications/app/idvr/src/main.cpp

build base/idvr/bin/idvr: link obj/applications/app/common/pb/generate/src/idvr.vehicle_signal.pb.o obj/applications/app/common/pb/generate/src/idvr.camera.pb.o obj/applications/app/idvr/src/dds/idvr.VehicleSigDdsReader.o obj/applications/app/idvr/src/dds/idvr.CamDdsReader.o obj/applications/app/idvr/src/media/venc/idvr.Venc.o obj/applications/app/idvr/src/media/yuvdraw/idvr.mvgl_bmp.o obj/applications/app/idvr/src/media/yuvdraw/idvr.mvgl_util.o obj/applications/app/idvr/src/media/yuvdraw/idvr.YUVDraw.o obj/applications/app/idvr/src/service/idvr.IdvrService.o obj/applications/app/idvr/src/rtsp/src/net/idvr.Acceptor.o obj/applications/app/idvr/src/rtsp/src/net/idvr.BufferReader.o obj/applications/app/idvr/src/rtsp/src/net/idvr.BufferWriter.o obj/applications/app/idvr/src/rtsp/src/net/idvr.EpollTaskScheduler.o obj/applications/app/idvr/src/rtsp/src/net/idvr.EventLoop.o obj/applications/app/idvr/src/rtsp/src/net/idvr.Logger.o obj/applications/app/idvr/src/rtsp/src/net/idvr.MemoryManager.o obj/applications/app/idvr/src/rtsp/src/net/idvr.NetInterface.o obj/applications/app/idvr/src/rtsp/src/net/idvr.Pipe.o obj/applications/app/idvr/src/rtsp/src/net/idvr.SelectTaskScheduler.o obj/applications/app/idvr/src/rtsp/src/net/idvr.SocketUtil.o obj/applications/app/idvr/src/rtsp/src/net/idvr.TaskScheduler.o obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpConnection.o obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpServer.o obj/applications/app/idvr/src/rtsp/src/net/idvr.TcpSocket.o obj/applications/app/idvr/src/rtsp/src/net/idvr.Timer.o obj/applications/app/idvr/src/rtsp/src/net/idvr.Timestamp.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.AACSource.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.DigestAuthentication.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.G711ASource.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.H264Parser.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.H264Source.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.H265Source.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.md5.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.MediaSession.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtpConnection.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspConnection.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspMessage.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspPuller.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspPusher.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.RtspServer.o obj/applications/app/idvr/src/rtsp/src/xop/idvr.VP8Source.o obj/applications/app/idvr/src/idvr.main.o ./libdata_proto_o.so ./libcppbase.so ./libmessage.so ./libmlog.so ./libmlog_static.a ./liblogd_static.a ./libtombstone_client.so ./libfilelog.so || obj/middleware/mlog/mlog_group.stamp obj/middleware/tombstone/tombstone_group.stamp obj/middleware/system/core/filelog/filelog_group.stamp
  ldflags = -L/home/<USER>/project/d4q/out/D4Q_debug -Wl,-rpath-link=/home/<USER>/project/d4q/out/D4Q_debug -lpthread --sysroot=/home/<USER>/project/d4q/platform/D4Q/build/sysroots -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -L/home/<USER>/project/d4q/platform/D4Q/build/sysroots/usr/hobot/lib -ldds -ldds_api -ldlt -lprotobuf -lflow -lglog -lxml2 -lyaml-cpp -ljsoncpp -lfilelog -lhbmem -lalog -lmultimedia
  libs =
  output_extension = 
  output_dir = base/idvr/bin
