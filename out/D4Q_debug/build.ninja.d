build.ninja: ../../applications/app/caninput/BUILD.gn ../../applications/app/canout/BUILD.gn ../../applications/app/canout/test/BUILD.gn ../../applications/app/common/BUILD.gn ../../applications/app/common/canio/BUILD.gn ../../applications/app/common/dbc/BUILD.gn ../../applications/app/common/libRMAgent/BUILD.gn ../../applications/app/common/libSigVerify/BUILD.gn ../../applications/app/common/libUniComm/BUILD.gn ../../applications/app/common/libcollect/BUILD.gn ../../applications/app/common/pb/BUILD.gn ../../applications/app/common/timehal/BUILD.gn ../../applications/app/common/utils/BUILD.gn ../../applications/app/diagnosis/BUILD.gn ../../applications/app/doip/BUILD.gn ../../applications/app/doip/src/libDoIP/BUILD.gn ../../applications/app/doip/src/libUDS/BUILD.gn ../../applications/app/doip/src/libUdsOnDoIP/BUILD.gn ../../applications/app/gnss/BUILD.gn ../../applications/app/idvr/BUILD.gn ../../applications/app/imu/BUILD.gn ../../applications/app/imu/utils/BUILD.gn ../../applications/app/phm/BUILD.gn ../../applications/app/qxids_sdk/BUILD.gn ../../applications/app/radar/BUILD.gn ../../applications/app/radar/test/BUILD.gn ../../applications/app/sr_hmi_client/BUILD.gn ../../applications/app/sr_hmi_service/BUILD.gn ../../applications/app/sr_hmi_service/sample/BUILD.gn ../../applications/app/sr_hmi_service/src/plugin/BUILD.gn ../../applications/app/state_manager/BUILD.gn ../../applications/app/state_manager/test/BUILD.gn ../../applications/app/takepoint_collect/BUILD.gn ../../applications/app/takepoint_test/BUILD.gn ../../applications/app/test/BUILD.gn ../../applications/app/test/dds_gtest/BUILD.gn ../../applications/bsp_app/app_avm_out/BUILD.gn ../../applications/bsp_app/avm_pym_stitch/BUILD.gn ../../applications/bsp_app/camera_service/BUILD.gn ../../applications/bsp_app/camera_service/tools/BUILD.gn ../../applications/bsp_app/can_utils/BUILD.gn ../../applications/bsp_app/encode_libflow/BUILD.gn ../../applications/bsp_app/eth_diagnosis/BUILD.gn ../../applications/bsp_app/flex_diagnosis/BUILD.gn ../../applications/bsp_app/flexidag_factory/BUILD.gn ../../applications/bsp_app/libdecode/BUILD.gn ../../applications/bsp_app/ota/BUILD.gn ../../applications/bsp_app/sync_status/BUILD.gn ../../applications/bsp_app/sys_perf_daemon/BUILD.gn ../../applications/bsp_app/timesync/BUILD.gn ../../build/minieye/.gn ../../build/minieye/BUILD.gn ../../build/minieye/config/BUILD.gn ../../build/minieye/config/BUILDCONFIG.gn ../../build/minieye/minieye.gni ../../build/minieye/os_var.gni ../../build/minieye/templates/copy.gni ../../build/minieye/templates/cxx.gni ../../build/minieye/templates/minieye_templates.gni ../../build/minieye/templates/prebuilt.gni ../../build/minieye/toolchain/BUILD.gn ../../build/minieye/toolchain/gcc.gni ../../build/minieye/tools/find_sources.py ../../middleware/communication/libevutil/BUILD.gn ../../middleware/communication/libnnflow/BUILD.gn ../../middleware/communication/nanomsg/BUILD.gn ../../middleware/daemon/BUILD.gn ../../middleware/daemon/sample/BUILD.gn ../../middleware/daemon/src/em/BUILD.gn ../../middleware/daemon/src/property/BUILD.gn ../../middleware/daemon/src/server/BUILD.gn ../../middleware/daemon/utils/BUILD.gn ../../middleware/dumpsys/BUILD.gn ../../middleware/ets_service/BUILD.gn ../../middleware/logd/BUILD.gn ../../middleware/logd/sample/BUILD.gn ../../middleware/logd/src/base/BUILD.gn ../../middleware/logd/src/libcutils/BUILD.gn ../../middleware/logd/src/liblog/BUILD.gn ../../middleware/logd/src/libpackagelistparser/BUILD.gn ../../middleware/logd/src/libsysutils/BUILD.gn ../../middleware/logd/src/logcat/BUILD.gn ../../middleware/logd/src/logd/BUILD.gn ../../middleware/logd/src/pcre/BUILD.gn ../../middleware/mlog/BUILD.gn ../../middleware/mlog/sample/BUILD.gn ../../middleware/persistency/BUILD.gn ../../middleware/persistency/utils/BUILD.gn ../../middleware/someip/BUILD.gn ../../middleware/system/core/filelog/BUILD.gn ../../middleware/system/core/filelog/demo/BUILD.gn ../../middleware/system/core/libcppbase/BUILD.gn ../../middleware/system/core/libjsonUtil/BUILD.gn ../../middleware/system/core/libmessage/BUILD.gn ../../middleware/system/core/libmessage/demo/BUILD.gn ../../middleware/system/tools/filemonitor/BUILD.gn ../../middleware/system/tools/log_global_save/BUILD.gn ../../middleware/system/tools/log_to_libflow/BUILD.gn ../../middleware/system/tools/scantree/BUILD.gn ../../middleware/system/tools/system_res_monitor/BUILD.gn ../../middleware/tombstone/BUILD.gn ../../middleware/tombstone/sample/BUILD.gn ../../middleware/tombstone/src/backtrace/BUILD.gn ../../middleware/tombstone/src/backtrace/demangle/BUILD.gn ../../middleware/tombstone/src/base/BUILD.gn ../../middleware/tombstone/src/debuggerd/BUILD.gn ../../middleware/tombstone/src/libcutils/BUILD.gn ../../middleware/tombstone/src/procinfo/BUILD.gn ../../middleware/tombstone/src/unwindstack/BUILD.gn ../../middleware/tombstone/src/unwindstack/lzma/BUILD.gn ../../middleware/tombstone/utils/BUILD.gn ../../middleware/upper_tester/BUILD.gn ./args.gn ../../platform/D4Q/build/platform.json ../../product.json