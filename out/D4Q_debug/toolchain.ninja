rule cc
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags} ${cflags_c} -c ${in} -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule cxx
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags_cc} -c ${in} -o ${out}
  description = CXX ${out}
  depfile = ${out}.d
  deps = gcc
rule asm
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc ${defines} ${include_dirs} ${asmflags} ${in} -c -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule alink
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-ar cr ${out} @"${out}.rsp"
  description = AR ${out}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule solink
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -shared -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} 
  description = SOLINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule link
  command = /home/<USER>/project/d4q/platform/D4Q/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} 
  description = LINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${output_dir}/${target_output_name}${output_extension}.rsp
  rspfile_content = ${in}
rule stamp
  command = touch ${out}
  description = STAMP ${out}
rule copy
  command = ln -f ${in} ${out} 2>/dev/null || (rm -rf ${out} && cp -af ${in} ${out})
  description = COPY ${in} ${out}

subninja obj/applications/app/caninput/TestDataDds.ninja
subninja obj/applications/app/caninput/TestDataIpc.ninja
subninja obj/applications/app/caninput/TestVehSignalHandler.ninja
subninja obj/applications/app/caninput/caninput.ninja
build base/caninput/config: copy ../../applications/app/caninput/config

build obj/applications/app/caninput/caninput_etc.stamp: stamp base/caninput/config
build obj/applications/app/caninput/caninput_group.stamp: stamp base/caninput/bin/caninput base/caninput/bin/ddsreader_test_caninput base/caninput/bin/module_test base/caninput/bin/TestDataIpc base/caninput/bin/TestDataDds base/caninput/bin/TestVehSignalHandler
build base/caninput/run.sh: copy ../../applications/app/caninput/run.sh

build obj/applications/app/caninput/caninput_run.stamp: stamp base/caninput/run.sh
build base/caninput/test.sh: copy ../../applications/app/caninput/test/module_test/test.sh

build obj/applications/app/caninput/caninput_test_run.stamp: stamp base/caninput/test.sh
subninja obj/applications/app/caninput/ddsreader_test_caninput.ninja
subninja obj/applications/app/caninput/module_test.ninja
subninja obj/applications/app/canout/canout.ninja
build base/canout/config: copy ../../applications/app/canout/config

build obj/applications/app/canout/canout_etc.stamp: stamp base/canout/config
build obj/applications/app/canout/canout_group.stamp: stamp base/canout/bin/canout obj/applications/app/canout/test/test.stamp
build base/canout/run.sh: copy ../../applications/app/canout/run.sh

build obj/applications/app/canout/canout_run.stamp: stamp base/canout/run.sh
build base/canout/test.sh: copy ../../applications/app/canout/test/module_test/test.sh

build obj/applications/app/canout/canout_test_run.stamp: stamp base/canout/test.sh
subninja obj/applications/app/canout/test/ddsreader_test.ninja
subninja obj/applications/app/canout/test/ddswriter_test.ninja
subninja obj/applications/app/canout/test/module_test.ninja
build base/canout/sample_json: copy ../../applications/app/canout/test/sample_json

build obj/applications/app/canout/test/sample_json.stamp: stamp base/canout/sample_json
build obj/applications/app/canout/test/test.stamp: stamp base/canout/bin/ddswriter_test base/canout/bin/ddsreader_test base/canout/bin/module_test base/canout/bin/test_dds_writer base/canout/bin/test_dds_reader
subninja obj/applications/app/canout/test/test_dds_reader.ninja
subninja obj/applications/app/canout/test/test_dds_writer.ninja
build obj/applications/app/common/common_group.stamp: stamp obj/applications/app/common/canio/library_canio_group.stamp ./libdata_proto_o.so obj/applications/app/common/utils/libcommon_utils_group.stamp obj/applications/app/common/libRMAgent/libRMAgent_group.stamp obj/applications/app/common/dbc/libdbc_group.stamp
subninja obj/applications/app/common/canio/CanIoTest.ninja
subninja obj/applications/app/common/canio/canHaldump.ninja
subninja obj/applications/app/common/canio/libcanio.ninja
build obj/applications/app/common/canio/library_canio_group.stamp: stamp ./libcanio.so bin/canHaldump bin/CanIoTest
subninja obj/applications/app/common/dbc/libdbc.ninja
build obj/applications/app/common/dbc/libdbc_group.stamp: stamp ./libdbc.so
subninja obj/applications/app/common/libRMAgent/RMAgentTest.ninja
subninja obj/applications/app/common/libRMAgent/get_version.ninja
subninja obj/applications/app/common/libRMAgent/libRMAgent.ninja
build obj/applications/app/common/libRMAgent/libRMAgent_group.stamp: stamp ./libRMAgent.so bin/get_version bin/RMAgentTest
subninja obj/applications/app/common/libSigVerify/libSigVerify.ninja
subninja obj/applications/app/common/libUniComm/libUniComm.ninja
subninja obj/applications/app/common/libcollect/libcollect.ninja
subninja obj/applications/app/common/pb/data_proto_o.ninja
build obj/applications/app/common/pb/libproto_group.stamp: stamp ./libdata_proto_o.so
subninja obj/applications/app/common/timehal/libminieyetimehal.ninja
build obj/applications/app/common/timehal/libminieyetimehal_group.stamp: stamp ./libminieyetimehal.so bin/sample_timehal
subninja obj/applications/app/common/timehal/sample_timehal.ninja
subninja obj/applications/app/common/utils/libcommon_utils.ninja
build obj/applications/app/common/utils/libcommon_utils_group.stamp: stamp ./libcommon_utils.so
subninja obj/applications/app/diagnosis/diagnosis.ninja
build obj/applications/app/diagnosis/diagnosis_group.stamp: stamp ./libdiagnosis.so base/diagnostic/bin/fault_server base/diagnostic/bin/report_fault base/diagnostic/bin/unittest_diagnosis obj/applications/app/diagnosis/diagnostic_etc.stamp
build base/diagnostic/config: copy ../../applications/app/diagnosis/config

build obj/applications/app/diagnosis/diagnostic_etc.stamp: stamp base/diagnostic/config
subninja obj/applications/app/diagnosis/fault_server.ninja
subninja obj/applications/app/diagnosis/report_fault.ninja
build base/diagnostic/test.sh: copy ../../applications/app/diagnosis/src/test.sh

build obj/applications/app/diagnosis/shell.stamp: stamp base/diagnostic/test.sh
subninja obj/applications/app/diagnosis/unittest_diagnosis.ninja
subninja obj/applications/app/doip/broadcast_test.ninja
subninja obj/applications/app/doip/doip.ninja
subninja obj/applications/app/doip/doipNnSrv.ninja
build base/doip/config: copy ../../applications/app/doip/config

build obj/applications/app/doip/doip_config.stamp: stamp base/doip/config
build obj/applications/app/doip/doip_group.stamp: stamp base/doip/bin/doip obj/applications/app/doip/doip_sh.stamp obj/applications/app/doip/doip_config.stamp base/doip/bin/nnTest bin/get_dtc base/doip/bin/broadcast_test obj/applications/app/doip/src/libDoIP/libDoIP_group.stamp obj/applications/app/doip/src/libUDS/libUDS_group.stamp obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP_group.stamp
build base/doip/run.sh: copy ../../applications/app/doip/run.sh

build obj/applications/app/doip/doip_sh.stamp: stamp base/doip/run.sh
subninja obj/applications/app/doip/get_dtc.ninja
subninja obj/applications/app/doip/nnTest.ninja
subninja obj/applications/app/doip/src/libDoIP/libDoIP.ninja
build obj/applications/app/doip/src/libDoIP/libDoIP_group.stamp: stamp ./libDoIP.so
subninja obj/applications/app/doip/src/libUDS/libUDS.ninja
build obj/applications/app/doip/src/libUDS/libUDS_group.stamp: stamp ./libUDS.so
subninja obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP.ninja
build obj/applications/app/doip/src/libUdsOnDoIP/libUdsOnDoIP_group.stamp: stamp ./libUdsOnDoIP.so
subninja obj/applications/app/gnss/GnssRawReader.ninja
subninja obj/applications/app/gnss/gnss.ninja
build obj/applications/app/gnss/gnss_group.stamp: stamp base/gnss/bin/gnss base/gnss/bin/GnssRawReader
subninja obj/applications/app/gnss/librtklib.ninja
subninja obj/applications/app/gnss/modtool.ninja
build base/gnss/run.sh: copy ../../applications/app/gnss/src/run.sh

build obj/applications/app/gnss/shell.stamp: stamp base/gnss/run.sh
build base/gnss/bin/upgrade.sh: copy ../../applications/app/gnss/src/tools/upgrade.sh

build obj/applications/app/gnss/shell1.stamp: stamp base/gnss/bin/upgrade.sh
build base/gnss/test.sh: copy ../../applications/app/gnss/src/test.sh

build obj/applications/app/gnss/shell2.stamp: stamp base/gnss/test.sh
build base/gnss/dumpGnssRaw.sh: copy ../../applications/app/gnss/src/dumpGnssRaw.sh

build obj/applications/app/gnss/shell3.stamp: stamp base/gnss/dumpGnssRaw.sh
build base/gnss/conf: copy ../../applications/app/gnss/conf

build obj/applications/app/gnss/this_conf_dir.stamp: stamp base/gnss/conf
subninja obj/applications/app/idvr/idvr.ninja
build obj/applications/app/idvr/idvr_group.stamp: stamp base/idvr/bin/idvr
build base/idvr/run.sh: copy ../../applications/app/idvr/src/run.sh

build obj/applications/app/idvr/shell.stamp: stamp base/idvr/run.sh
build base/idvr/conf: copy ../../applications/app/idvr/conf

build obj/applications/app/idvr/this_conf_dir.stamp: stamp base/idvr/conf
subninja obj/applications/app/imu/ImuClientTest.ninja
subninja obj/applications/app/imu/ddsreader_imu.ninja
subninja obj/applications/app/imu/ddswriter_imu.ninja
build base/imu/imu_dv_test.sh: copy ../../applications/app/imu/imu_dv_test.sh

build obj/applications/app/imu/dv_run.stamp: stamp base/imu/imu_dv_test.sh
subninja obj/applications/app/imu/dv_test.ninja
subninja obj/applications/app/imu/imu_app.ninja
build base/imu/config: copy ../../applications/app/imu/config

build obj/applications/app/imu/imu_etc.stamp: stamp base/imu/config
build obj/applications/app/imu/imu_group.stamp: stamp base/imu/bin/imu_app ./libImuClient.so base/imu/bin/ddsreader_imu base/imu/bin/imu_recv_demo base/imu/bin/ddswriter_imu base/imu/bin/module_test base/imu/bin/dv_test base/imu/bin/ImuClientTest base/imu/bin/unittest obj/applications/app/imu/utils/utils.stamp
subninja obj/applications/app/imu/imu_recv_demo.ninja
build base/imu/run.sh: copy ../../applications/app/imu/run.sh

build obj/applications/app/imu/imu_run.stamp: stamp base/imu/run.sh
build base/imu/test.sh: copy ../../applications/app/imu/test/module_test/test.sh

build obj/applications/app/imu/imu_test_run.stamp: stamp base/imu/test.sh
subninja obj/applications/app/imu/libImuClient.ninja
subninja obj/applications/app/imu/module_test.ninja
subninja obj/applications/app/imu/unittest.ninja
subninja obj/applications/app/imu/utils/imu_ota.ninja
subninja obj/applications/app/imu/utils/imu_sim.ninja
build base/imu/imu_sim/config: copy ../../applications/app/imu/utils/imu_sim/config

build obj/applications/app/imu/utils/imu_sim_etc.stamp: stamp base/imu/imu_sim/config
build base/imu/imu_sim/readme.txt: copy ../../applications/app/imu/utils/imu_sim/readme.txt

build obj/applications/app/imu/utils/imu_sim_readme.stamp: stamp base/imu/imu_sim/readme.txt
build base/imu/imu_sim/run.sh: copy ../../applications/app/imu/utils/imu_sim/run.sh

build obj/applications/app/imu/utils/imu_sim_run.stamp: stamp base/imu/imu_sim/run.sh
build base/imu/lib: copy ../../applications/app/imu/utils/imu_ota/ota_sdk/lib

build obj/applications/app/imu/utils/ota_lib.stamp: stamp base/imu/lib
build base/imu/imu_ota.sh: copy ../../applications/app/imu/utils/imu_ota/imu_ota.sh

build obj/applications/app/imu/utils/ota_run.stamp: stamp base/imu/imu_ota.sh
subninja obj/applications/app/imu/utils/sim_dds_test.ninja
build obj/applications/app/imu/utils/utils.stamp: stamp base/imu/bin/imu_ota base/imu/imu_sim/bin/imu_sim base/imu/imu_sim/bin/sim_dds_test
subninja obj/applications/app/phm/PhmAgent.ninja
subninja obj/applications/app/phm/gtest.ninja
subninja obj/applications/app/phm/phm.ninja
subninja obj/applications/app/phm/phmAgtSample.ninja
build base/phm/etc: copy ../../applications/app/phm/etc

build obj/applications/app/phm/phm_cfg_dir.stamp: stamp base/phm/etc
build obj/applications/app/phm/phm_group.stamp: stamp base/phm/bin/phm ./libPhmAgent.so bin/phmAgtSample bin/gtest obj/applications/app/phm/phm_cfg_dir.stamp obj/applications/app/phm/run_sh.stamp
build base/phm/bin/run.sh: copy ../../applications/app/phm/src/run.sh

build obj/applications/app/phm/run_sh.stamp: stamp base/phm/bin/run.sh
subninja obj/applications/app/qxids_sdk/ddsreader_test.ninja
subninja obj/applications/app/qxids_sdk/ddswriter_test.ninja
subninja obj/applications/app/qxids_sdk/module_test.ninja
build base/qxids_sdk/run_nssr.sh: copy ../../applications/app/qxids_sdk/run_nssr.sh

build obj/applications/app/qxids_sdk/nssr_run.stamp: stamp base/qxids_sdk/run_nssr.sh
subninja obj/applications/app/qxids_sdk/ppp_engine_app.ninja
subninja obj/applications/app/qxids_sdk/ppp_engine_app_bak.ninja
build base/qxids_sdk/run_pppe.sh: copy ../../applications/app/qxids_sdk/run_pppe.sh

build obj/applications/app/qxids_sdk/pppe_run.stamp: stamp base/qxids_sdk/run_pppe.sh
build base/qxids_sdk/config: copy ../../applications/app/qxids_sdk/config

build obj/applications/app/qxids_sdk/qxids_sdk_etc.stamp: stamp base/qxids_sdk/config
build obj/applications/app/qxids_sdk/qxids_sdk_group.stamp: stamp base/qxids_sdk/bin/ppp_engine_app base/qxids_sdk/bin/ppp_engine_app_bak base/qxids_sdk/bin/service_nssr base/qxids_sdk/bin/ddsreader_test base/qxids_sdk/bin/module_test
build base/qxids_sdk/libs: copy ../../applications/app/qxids_sdk/sdk/libs

build obj/applications/app/qxids_sdk/qxids_sdk_libs.stamp: stamp base/qxids_sdk/libs
build base/qxids_sdk/run.sh: copy ../../applications/app/qxids_sdk/run.sh

build obj/applications/app/qxids_sdk/qxids_sdk_run.stamp: stamp base/qxids_sdk/run.sh
build base/qxids_sdk/test.sh: copy ../../applications/app/qxids_sdk/test/test.sh

build obj/applications/app/qxids_sdk/qxids_sdk_test_run.stamp: stamp base/qxids_sdk/test.sh
build base/qxids_sdk/test_pos_status.sh: copy ../../applications/app/qxids_sdk/test_pos_status.sh

build obj/applications/app/qxids_sdk/qxids_sdk_test_sh.stamp: stamp base/qxids_sdk/test_pos_status.sh
build base/qxids_sdk/sdk_version: copy ../../applications/app/qxids_sdk/sdk_version

build obj/applications/app/qxids_sdk/sdk_version_file.stamp: stamp base/qxids_sdk/sdk_version
subninja obj/applications/app/qxids_sdk/service_nssr.ninja
subninja obj/applications/app/radar/radar.ninja
build base/radar/config: copy ../../applications/app/radar/config

build obj/applications/app/radar/radar_etc.stamp: stamp base/radar/config
build obj/applications/app/radar/radar_group.stamp: stamp base/radar/radar base/radar/client_radar obj/applications/app/radar/radar_etc.stamp obj/applications/app/radar/radar_test_sh.stamp obj/applications/app/radar/radar_run.stamp base/radar/UintTest
build base/radar/run.sh: copy ../../applications/app/radar/run.sh

build obj/applications/app/radar/radar_run.stamp: stamp base/radar/run.sh
build base/radar/test.sh: copy ../../applications/app/radar/test/test.sh

build obj/applications/app/radar/radar_test_sh.stamp: stamp base/radar/test.sh
subninja obj/applications/app/radar/test/UintTest.ninja
subninja obj/applications/app/radar/test/client_radar.ninja
subninja obj/applications/app/sr_hmi_client/JetouSdMapTest.ninja
subninja obj/applications/app/sr_hmi_client/sr_hmi_client.ninja
build base/sr_hmi_client/config: copy ../../applications/app/sr_hmi_client/config

build obj/applications/app/sr_hmi_client/sr_hmi_client_cfg.stamp: stamp base/sr_hmi_client/config
build obj/applications/app/sr_hmi_client/sr_hmi_client_group.stamp: stamp base/sr_hmi_client/bin/sr_hmi_client base/sr_hmi_client/bin/JetouSdMapTest base/sr_hmi_client/bin/tboxMsgTest
build base/sr_hmi_client/run.sh: copy ../../applications/app/sr_hmi_client/run.sh

build obj/applications/app/sr_hmi_client/sr_hmi_client_run.stamp: stamp base/sr_hmi_client/run.sh
subninja obj/applications/app/sr_hmi_client/tboxMsgTest.ninja
subninja obj/applications/app/sr_hmi_service/SrDataSdk.ninja
subninja obj/applications/app/sr_hmi_service/libsensor_status_plugin.ninja
build base/sr_hmi_service/run_routing.sh: copy ../../applications/app/sr_hmi_service/src/routing/run_routing.sh

build obj/applications/app/sr_hmi_service/routing_run.stamp: stamp base/sr_hmi_service/run_routing.sh
subninja obj/applications/app/sr_hmi_service/someip_routing.ninja
subninja obj/applications/app/sr_hmi_service/sr_client_simulation.ninja
subninja obj/applications/app/sr_hmi_service/sr_hmi_service.ninja
build base/sr_hmi_service/config: copy ../../applications/app/sr_hmi_service/config

build obj/applications/app/sr_hmi_service/sr_hmi_service_cfg.stamp: stamp base/sr_hmi_service/config
build base/sr_hmi_service/run.sh: copy ../../applications/app/sr_hmi_service/src/sr_hmi_service/run.sh

build obj/applications/app/sr_hmi_service/sr_hmi_service_run.stamp: stamp base/sr_hmi_service/run.sh
subninja obj/applications/app/sr_hmi_service/sr_input_test.ninja
build obj/applications/app/sr_hmi_service/sr_service_group.stamp: stamp ./libSrDataSdk.so base/sr_hmi_service/bin/sr_hmi_service base/sr_hmi_service/bin/sr_input_test obj/applications/app/sr_hmi_service/sample/sr_sample_group.stamp obj/applications/app/sr_hmi_service/src/plugin/sr_plugin_group.stamp ./libsensor_status_plugin.so base/sr_hmi_service/bin/someip_routing
subninja obj/applications/app/sr_hmi_service/sample/AdasHmiTest.ninja
build base/sr_hmi_service/run_AdasHmi.sh: copy ../../applications/app/sr_hmi_service/sample/AdasHmi/run_AdasHmi.sh

build obj/applications/app/sr_hmi_service/sample/sr_adashmi_run.stamp: stamp base/sr_hmi_service/run_AdasHmi.sh
build obj/applications/app/sr_hmi_service/sample/sr_sample_group.stamp: stamp base/sr_hmi_service/bin/AdasHmiTest
subninja obj/applications/app/sr_hmi_service/src/plugin/libapa_plugin_example.ninja
subninja obj/applications/app/sr_hmi_service/src/plugin/libsr_core_manager.ninja
subninja obj/applications/app/sr_hmi_service/src/plugin/libvehicle_plugin_example.ninja
subninja obj/applications/app/sr_hmi_service/src/plugin/plugin_service.ninja
build base/sr_hmi_service/config/sr_plugin.json: copy ../../applications/app/sr_hmi_service/src/plugin/config/sr_plugin.json

build obj/applications/app/sr_hmi_service/src/plugin/sr_hmi_plugin_cfg.stamp: stamp base/sr_hmi_service/config/sr_plugin.json
build obj/applications/app/sr_hmi_service/src/plugin/sr_plugin_group.stamp: stamp base/sr_hmi_service/bin/plugin_service ./libsr_core_manager.so ./libvehicle_plugin_example.so ./libapa_plugin_example.so obj/applications/app/sr_hmi_service/src/plugin/sr_hmi_plugin_cfg.stamp
subninja obj/applications/app/state_manager/StateAgent.ninja
subninja obj/applications/app/state_manager/StateTrigger.ninja
subninja obj/applications/app/state_manager/state_change_test.ninja
subninja obj/applications/app/state_manager/state_client_test.ninja
subninja obj/applications/app/state_manager/state_manager.ninja
build base/state_manager/config: copy ../../applications/app/state_manager/config

build obj/applications/app/state_manager/state_manager_etc.stamp: stamp base/state_manager/config
build obj/applications/app/state_manager/state_manager_group.stamp: stamp ./libStateAgent.so ./libStateTrigger.so base/state_manager/bin/state_manager base/state_manager/bin/state_client_test base/state_manager/bin/state_change_test obj/applications/app/state_manager/test/state_test.stamp
build base/state_manager/run.sh: copy ../../applications/app/state_manager/src/stateman/run.sh

build obj/applications/app/state_manager/state_manager_run.stamp: stamp base/state_manager/run.sh
subninja obj/applications/app/state_manager/test/classtest_state.ninja
build obj/applications/app/state_manager/test/state_test.stamp: stamp base/state_manager/bin/unittest_state base/state_manager/bin/classtest_state
build base/state_manager/run_stresstest.sh: copy ../../applications/app/state_manager/test/stresstest/run_stresstest.sh

build obj/applications/app/state_manager/test/stresstest_run.stamp: stamp base/state_manager/run_stresstest.sh
subninja obj/applications/app/state_manager/test/unittest_state.ninja
subninja obj/applications/app/takepoint_collect/mcap_reader.ninja
subninja obj/applications/app/takepoint_collect/takepoint_collect.ninja
build base/takepoint_collect/config: copy ../../applications/app/takepoint_collect/config

build obj/applications/app/takepoint_collect/takepoint_collect_config.stamp: stamp base/takepoint_collect/config
build obj/applications/app/takepoint_collect/takepoint_collect_group.stamp: stamp base/takepoint_collect/bin/takepoint_collect obj/applications/app/takepoint_collect/takepoint_thirdparty.stamp obj/applications/app/takepoint_collect/takepoint_collect_sh.stamp obj/applications/app/takepoint_collect/takepoint_collect_config.stamp obj/applications/app/takepoint_collect/takepoint_collect_util.stamp base/bin/takepoint_collect_tools_decrypt base/bin/mcap_reader
build base/takepoint_collect/run.sh: copy ../../applications/app/takepoint_collect/run.sh

build obj/applications/app/takepoint_collect/takepoint_collect_sh.stamp: stamp base/takepoint_collect/run.sh
subninja obj/applications/app/takepoint_collect/takepoint_collect_tools_decrypt.ninja
build base/takepoint_collect/bin/security_ntsp: copy ../../applications/app/takepoint_collect/util/security_ntsp

build obj/applications/app/takepoint_collect/takepoint_collect_util.stamp: stamp base/takepoint_collect/bin/security_ntsp
build base/takepoint_collect/lib: copy ../../applications/app/takepoint_collect/src/thirdparty/lib

build obj/applications/app/takepoint_collect/takepoint_thirdparty.stamp: stamp base/takepoint_collect/lib
subninja obj/applications/app/takepoint_test/takepoint_test.ninja
build obj/applications/app/takepoint_test/takepoint_test_group.stamp: stamp base/takepoint_test/bin/takepoint_test obj/applications/app/takepoint_test/takepoint_test_sh.stamp
build base/takepoint_test/run.sh: copy ../../applications/app/takepoint_test/run.sh

build obj/applications/app/takepoint_test/takepoint_test_sh.stamp: stamp base/takepoint_test/run.sh
build obj/applications/app/test/test_group.stamp: stamp obj/applications/app/test/dds_gtest/dds_gtest_group.stamp
build obj/applications/app/test/dds_gtest/dds_gtest_group.stamp: stamp base/test/dds_test/bin/dds_unittest
subninja obj/applications/app/test/dds_gtest/dds_unittest.ninja
subninja obj/applications/bsp_app/app_avm_out/app_avm_out.ninja
build obj/applications/bsp_app/app_avm_out/app_avm_out_group.stamp: stamp base/vout/bin/app_avm_out base/vout/bin/voutdiagnose
build base/vout/conf: copy ../../applications/bsp_app/app_avm_out/conf

build obj/applications/bsp_app/app_avm_out/vout_config.stamp: stamp base/vout/conf
build base/vout/run.sh: copy ../../applications/bsp_app/app_avm_out/run.sh

build obj/applications/bsp_app/app_avm_out/vout_run.stamp: stamp base/vout/run.sh
subninja obj/applications/bsp_app/app_avm_out/voutdiagnose.ninja
subninja obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch.ninja
build base/stitch/conf: copy ../../applications/bsp_app/avm_pym_stitch/conf

build obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_conf.stamp: stamp base/stitch/conf
build obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_group.stamp: stamp base/stitch/bin/avm_pym_stitch obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_conf.stamp
build base/stitch/run.sh: copy ../../applications/bsp_app/avm_pym_stitch/run.sh

build obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_run.stamp: stamp base/stitch/run.sh
subninja obj/applications/bsp_app/camera_service/UintTest.ninja
subninja obj/applications/bsp_app/camera_service/camera_client.ninja
build base/camera/run.sh: copy ../../applications/bsp_app/camera_service/run.sh

build obj/applications/bsp_app/camera_service/camera_run.stamp: stamp base/camera/run.sh
subninja obj/applications/bsp_app/camera_service/camera_service.ninja
build base/camera/conf: copy ../../applications/bsp_app/camera_service/conf

build obj/applications/bsp_app/camera_service/camera_service_conf.stamp: stamp base/camera/conf
build obj/applications/bsp_app/camera_service/camera_service_group.stamp: stamp base/camera/bin/camera_service ./libCameraClient.so base/camera/bin/camera_client base/camera/bin/UintTest base/camera/bin/camera_tool base/camera/bin/getcameraimage base/camera/bin/camera_dds_recv
build base/camera/test.sh: copy ../../applications/bsp_app/camera_service/test.sh

build obj/applications/bsp_app/camera_service/camera_test.stamp: stamp base/camera/test.sh
build base/camera/run_cam_libflow.sh: copy ../../applications/bsp_app/camera_service/run_cam_libflow.sh

build obj/applications/bsp_app/camera_service/encode_run.stamp: stamp base/camera/run_cam_libflow.sh
subninja obj/applications/bsp_app/camera_service/libCameraClient.ninja
build base/camera/run_camera_adas.sh: copy ../../applications/bsp_app/camera_service/run_camera_adas.sh

build obj/applications/bsp_app/camera_service/run_camera_adas.stamp: stamp base/camera/run_camera_adas.sh
build base/camera/run_camera_apa.sh: copy ../../applications/bsp_app/camera_service/run_camera_apa.sh

build obj/applications/bsp_app/camera_service/run_camera_apa.stamp: stamp base/camera/run_camera_apa.sh
subninja obj/applications/bsp_app/camera_service/tools/camera_dds_recv.ninja
subninja obj/applications/bsp_app/camera_service/tools/camera_tool.ninja
subninja obj/applications/bsp_app/camera_service/tools/getcameraimage.ninja
build obj/applications/bsp_app/can_utils/can_utils_group.stamp: stamp bin/candump bin/cansend
subninja obj/applications/bsp_app/can_utils/candump.ninja
subninja obj/applications/bsp_app/can_utils/cansend.ninja
subninja obj/applications/bsp_app/encode_libflow/camera_encode_libflow.ninja
build obj/applications/bsp_app/encode_libflow/camera_encode_libflow_group.stamp: stamp base/camera/bin/camera_encode_libflow
subninja obj/applications/bsp_app/eth_diagnosis/eth_diagnosis.ninja
build base/eth_diagnosis/run.sh: copy ../../applications/bsp_app/eth_diagnosis/run.sh

build obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_etc.stamp: stamp base/eth_diagnosis/run.sh
build obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_group.stamp: stamp base/eth_diagnosis/bin/eth_diagnosis base/eth_diagnosis/bin/eth_diagnosis_subscript
subninja obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_subscript.ninja
subninja obj/applications/bsp_app/flex_diagnosis/can_view.ninja
subninja obj/applications/bsp_app/flex_diagnosis/flex_diagnosis.ninja
build base/flex_diagnosis/config.json: copy ../../applications/bsp_app/flex_diagnosis/conf/config.json

build obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_etc.stamp: stamp base/flex_diagnosis/config.json
build obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_group.stamp: stamp base/flex_diagnosis/flex_diagnosis obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_etc.stamp base/flex_diagnosis/can_view obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_tools.stamp
build base/flex_diagnosis/me_do_bootcnt.sh: copy ../../applications/bsp_app/flex_diagnosis/tools/me_do_bootcnt.sh

build obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_tools.stamp: stamp base/flex_diagnosis/me_do_bootcnt.sh
subninja obj/applications/bsp_app/flexidag_factory/flexidag_factory.ninja
build base/flexidag_factory/factory_dds_config.json: copy ../../applications/bsp_app/flexidag_factory/conf/factory_dds_config.json

build obj/applications/bsp_app/flexidag_factory/flexidag_factory_etc.stamp: stamp base/flexidag_factory/factory_dds_config.json
build obj/applications/bsp_app/flexidag_factory/flexidag_factory_group.stamp: stamp base/bin/flexidag_factory base/flexidag_factory/isotp_tester obj/applications/bsp_app/flexidag_factory/flexidag_factory_etc.stamp obj/applications/bsp_app/flexidag_factory/flexidag_factory_shell.stamp obj/applications/bsp_app/flexidag_factory/get_mcu_version_shell.stamp
build base/bin/minieye.sh: copy ../../applications/bsp_app/flexidag_factory/minieye.sh

build obj/applications/bsp_app/flexidag_factory/flexidag_factory_shell.stamp: stamp base/bin/minieye.sh
build base/bin/get_version_mcu.sh: copy ../../applications/bsp_app/flexidag_factory/tools/get_version_mcu.sh

build obj/applications/bsp_app/flexidag_factory/get_mcu_version_shell.stamp: stamp base/bin/get_version_mcu.sh
subninja obj/applications/bsp_app/flexidag_factory/isotp_tester.ninja
build obj/applications/bsp_app/libdecode/libdecode_group.stamp: stamp ./librtosdecmjpegV5.so
subninja obj/applications/bsp_app/libdecode/librtosdecmjpegV5.ninja
build libs/libabinstaller_static.a: copy ../../applications/bsp_app/ota/src/depends/installer/lib/libabinstaller_static.a

build obj/applications/bsp_app/ota/libabinstaller_static.stamp: stamp libs/libabinstaller_static.a
build libs/libabinstallerpl_static.a: copy ../../applications/bsp_app/ota/src/depends/installer/lib/libabinstallerpl_static.a

build obj/applications/bsp_app/ota/libabinstallerpl_static.stamp: stamp libs/libabinstallerpl_static.a
subninja obj/applications/bsp_app/ota/libupgrade_api.ninja
subninja obj/applications/bsp_app/ota/mi_ota_tool.ninja
subninja obj/applications/bsp_app/ota/ota_burn.ninja
build obj/applications/bsp_app/ota/upgrade_group.stamp: stamp ./libupgrade_api.so bin/mi_ota_tool bin/ota_burn
subninja obj/applications/bsp_app/sync_status/sync_status.ninja
build base/gpu_performance_dump: copy ../../applications/bsp_app/sys_perf_daemon/gpu_performance_dump

build obj/applications/bsp_app/sys_perf_daemon/gpu_performance_dump.stamp: stamp base/gpu_performance_dump
build libs/libprotobuf_arm64.a: copy ../../applications/bsp_app/sys_perf_daemon/src/lib/libprotobuf_arm64.a

build obj/applications/bsp_app/sys_perf_daemon/res_protobuf.stamp: stamp libs/libprotobuf_arm64.a
subninja obj/applications/bsp_app/sys_perf_daemon/sys_perf_daemon.ninja
build obj/applications/bsp_app/sys_perf_daemon/sys_perf_daemon_group.stamp: stamp bin/sys_perf_daemon obj/applications/bsp_app/sys_perf_daemon/gpu_performance_dump.stamp
build base/timesync/gnss_sync_to_mcu_phc.sh: copy ../../applications/bsp_app/timesync/gnss_sync_to_mcu_phc.sh

build obj/applications/bsp_app/timesync/gnss_sync_to_mcu_phc.stamp: stamp base/timesync/gnss_sync_to_mcu_phc.sh
build base/timesync/mcu_phc_sync_to_system.sh: copy ../../applications/bsp_app/timesync/mcu_phc_sync_to_system.sh

build obj/applications/bsp_app/timesync/mcu_phc_sync_to_system.stamp: stamp base/timesync/mcu_phc_sync_to_system.sh
build base/timesync/rtc_sync_to_phc0.sh: copy ../../applications/bsp_app/timesync/rtc_sync_to_phc0.sh

build obj/applications/bsp_app/timesync/rtc_sync_to_phc0.stamp: stamp base/timesync/rtc_sync_to_phc0.sh
subninja obj/applications/bsp_app/timesync/timesync.ninja
build base/timesync/conf/timesync_ipcf_config.json: copy ../../applications/bsp_app/timesync/timesync_ipcf_config.json

build obj/applications/bsp_app/timesync/timesync_config_etc.stamp: stamp base/timesync/conf/timesync_ipcf_config.json
build base/timesync/run.sh: copy ../../applications/bsp_app/timesync/run.sh

build obj/applications/bsp_app/timesync/timesync_etc.stamp: stamp base/timesync/run.sh
build obj/applications/bsp_app/timesync/timesync_group.stamp: stamp base/timesync/bin/timesync
build base/timesync/test.sh: copy ../../applications/bsp_app/timesync/test.sh

build obj/applications/bsp_app/timesync/timesync_test_sh.stamp: stamp base/timesync/test.sh
build obj/build/minieye/all.stamp: stamp obj/applications/app/phm/phm_group.stamp obj/applications/app/sr_hmi_client/sr_hmi_client_group.stamp obj/applications/app/idvr/idvr_group.stamp obj/applications/app/test/test_group.stamp obj/applications/bsp_app/ota/upgrade_group.stamp obj/applications/app/sr_hmi_service/sr_service_group.stamp obj/middleware/someip/vsomeip3_group.stamp obj/middleware/ets_service/ets_service_group.stamp obj/middleware/upper_tester/upper_tester_group.stamp obj/applications/app/state_manager/state_manager_group.stamp obj/applications/app/canout/canout_group.stamp obj/applications/app/caninput/caninput_group.stamp obj/applications/app/qxids_sdk/qxids_sdk_group.stamp obj/applications/app/imu/imu_group.stamp obj/applications/bsp_app/libdecode/libdecode_group.stamp obj/applications/bsp_app/app_avm_out/app_avm_out_group.stamp obj/applications/bsp_app/encode_libflow/camera_encode_libflow_group.stamp obj/applications/bsp_app/camera_service/camera_service_group.stamp obj/applications/bsp_app/avm_pym_stitch/avm_pym_stitch_group.stamp obj/applications/bsp_app/timesync/timesync_group.stamp misc/tools/sync_status obj/applications/bsp_app/can_utils/can_utils_group.stamp obj/applications/bsp_app/eth_diagnosis/eth_diagnosis_group.stamp obj/applications/app/common/common_group.stamp obj/applications/app/gnss/gnss_group.stamp obj/applications/app/common/timehal/libminieyetimehal_group.stamp obj/middleware/communication/nanomsg/library_nanomsg_group.stamp obj/middleware/dumpsys/dumpsys_group.stamp obj/middleware/system/core/filelog/demo/filelog_demo.stamp bin/scantree bin/file_monitor bin/sample_message obj/middleware/daemon/daemon_group.stamp obj/middleware/mlog/sample/mlog_sample.stamp obj/middleware/tombstone/tombstone_group.stamp obj/middleware/logd/logd_group.stamp bin/system_res_monitor obj/applications/bsp_app/flex_diagnosis/flex_diagnosis_group.stamp obj/applications/bsp_app/flexidag_factory/flexidag_factory_group.stamp bin/sys_perf_daemon obj/applications/app/diagnosis/diagnosis_group.stamp obj/applications/app/doip/doip_group.stamp obj/applications/app/radar/radar_group.stamp base/sr_hmi_service/bin/sr_client_simulation bin/log_to_dds obj/middleware/system/tools/log_global_save/log_tools.stamp obj/applications/app/takepoint_collect/takepoint_collect_group.stamp obj/applications/app/takepoint_test/takepoint_test_group.stamp
subninja obj/middleware/communication/libevutil/libevutil.ninja
subninja obj/middleware/communication/libnnflow/nnflow.ninja
build obj/middleware/communication/libnnflow/nnflow_group.stamp: stamp ./libnnflow.so base/nnflow/nnpubsub base/nnflow/nnreqrep base/nnflow/sample_nnflow
subninja obj/middleware/communication/libnnflow/nnpubsub.ninja
subninja obj/middleware/communication/libnnflow/nnreqrep.ninja
subninja obj/middleware/communication/libnnflow/sample_nnflow.ninja
subninja obj/middleware/communication/nanomsg/PairTest.ninja
subninja obj/middleware/communication/nanomsg/PubSubTest.ninja
subninja obj/middleware/communication/nanomsg/ReqRepTest.ninja
subninja obj/middleware/communication/nanomsg/SurveyTest.ninja
subninja obj/middleware/communication/nanomsg/libnnmsg.ninja
build obj/middleware/communication/nanomsg/library_nanomsg_group.stamp: stamp ./libnnmsg.so bin/ReqRepTest bin/PubSubTest bin/PairTest
build obj/middleware/daemon/daemon_group.stamp: stamp bin/daemon ./libdaemon.so obj/middleware/daemon/utils/utils.stamp obj/middleware/daemon/sample/daemon_sample.stamp
build obj/middleware/daemon/sample/daemon_sample.stamp: stamp bin/sample_em
subninja obj/middleware/daemon/sample/sample_em.ninja
subninja obj/middleware/daemon/src/em/daemon.ninja
subninja obj/middleware/daemon/src/property/property.ninja
subninja obj/middleware/daemon/src/server/daemon.ninja
subninja obj/middleware/daemon/utils/dumprc.ninja
subninja obj/middleware/daemon/utils/getsystemprop.ninja
subninja obj/middleware/daemon/utils/setsystemprop.ninja
subninja obj/middleware/daemon/utils/startrc.ninja
subninja obj/middleware/daemon/utils/stoprc.ninja
build obj/middleware/daemon/utils/utils.stamp: stamp bin/getsystemprop bin/setsystemprop bin/startrc bin/stoprc
subninja obj/middleware/dumpsys/dumpsys.ninja
build obj/middleware/dumpsys/dumpsys_group.stamp: stamp bin/dumpsys bin/sample_dumpsys ./libdumpsys_interface.so
subninja obj/middleware/dumpsys/libdumpsys_interface.ninja
subninja obj/middleware/dumpsys/sample_dumpsys.ninja
subninja obj/middleware/ets_service/ets_service.ninja
build base/ets_service/config: copy ../../middleware/ets_service/config

build obj/middleware/ets_service/ets_service_etc.stamp: stamp base/ets_service/config
build obj/middleware/ets_service/ets_service_group.stamp: stamp base/ets_service/bin/ets_service
build base/ets_service/run.sh: copy ../../middleware/ets_service/src/run.sh

build obj/middleware/ets_service/ets_service_run.stamp: stamp base/ets_service/run.sh
build obj/middleware/logd/logd_group.stamp: stamp bin/logdcat bin/logd ./liblogd_static.a bin/sample_logd
subninja obj/middleware/logd/sample/sample_logd.ninja
subninja obj/middleware/logd/src/base/base_logd.ninja
subninja obj/middleware/logd/src/libcutils/libcutils_logd.ninja
subninja obj/middleware/logd/src/liblog/liblogd.ninja
subninja obj/middleware/logd/src/liblog/liblogd_static.ninja
subninja obj/middleware/logd/src/libpackagelistparser/libpackagelistparser_logd.ninja
subninja obj/middleware/logd/src/libsysutils/libsysutils_logd.ninja
subninja obj/middleware/logd/src/logcat/logdcat.ninja
subninja obj/middleware/logd/src/logd/logd.ninja
subninja obj/middleware/logd/src/pcre/libpcre_logd.ninja
subninja obj/middleware/logd/src/pcre/libpcrecpp_logd.ninja
subninja obj/middleware/mlog/libmlog.ninja
subninja obj/middleware/mlog/libmlog_static.ninja
build obj/middleware/mlog/mlog_group.stamp: stamp ./libmlog.so ./libmlog_static.a bin/mlogcat
subninja obj/middleware/mlog/mlogcat.ninja
build obj/middleware/mlog/sample/mlog_sample.stamp: stamp bin/sample_mlog bin/mlogsend
subninja obj/middleware/mlog/sample/mlogsend.ninja
subninja obj/middleware/mlog/sample/sample_mlog.ninja
subninja obj/middleware/persistency/libpersistency.ninja
subninja obj/middleware/persistency/libpersistency_common.ninja
subninja obj/middleware/persistency/persistency.ninja
build obj/middleware/persistency/persistency_group.stamp: stamp bin/persistency obj/middleware/persistency/utils/utils.stamp
subninja obj/middleware/persistency/utils/getshareconfig.ninja
subninja obj/middleware/persistency/utils/setshareconfig.ninja
build obj/middleware/persistency/utils/utils.stamp: stamp bin/setshareconfig bin/getshareconfig
rule __middleware_someip_symlink_vsomeip3___build_minieye_toolchain_gcc__rule
  command = python ../../middleware/someip/src/create_symlinks.py /home/<USER>/project/d4q/out/D4Q_debug libvsomeip3.so.3.5.1
  description = ACTION //middleware/someip:symlink_vsomeip3(//build/minieye/toolchain:gcc)
  restat = 1

build libvsomeip3.so.3 libvsomeip3.so.3.5.1: __middleware_someip_symlink_vsomeip3___build_minieye_toolchain_gcc__rule | ../../middleware/someip/src/create_symlinks.py ./libvsomeip3.so

build obj/middleware/someip/symlink_vsomeip3.stamp: stamp libvsomeip3.so.3 libvsomeip3.so.3.5.1
rule __middleware_someip_symlink_vsomeip3_cfg___build_minieye_toolchain_gcc__rule
  command = python ../../middleware/someip/src/create_symlinks.py /home/<USER>/project/d4q/out/D4Q_debug libvsomeip3-cfg.so.3.5.1
  description = ACTION //middleware/someip:symlink_vsomeip3_cfg(//build/minieye/toolchain:gcc)
  restat = 1

build libvsomeip3-cfg.so.3 libvsomeip3-cfg.so.3.5.1: __middleware_someip_symlink_vsomeip3_cfg___build_minieye_toolchain_gcc__rule | ../../middleware/someip/src/create_symlinks.py ./libvsomeip3-cfg.so

build obj/middleware/someip/symlink_vsomeip3_cfg.stamp: stamp libvsomeip3-cfg.so.3 libvsomeip3-cfg.so.3.5.1
rule __middleware_someip_symlink_vsomeip3_e2e___build_minieye_toolchain_gcc__rule
  command = python ../../middleware/someip/src/create_symlinks.py /home/<USER>/project/d4q/out/D4Q_debug libvsomeip3-e2e.so.3.5.1
  description = ACTION //middleware/someip:symlink_vsomeip3_e2e(//build/minieye/toolchain:gcc)
  restat = 1

build libvsomeip3-e2e.so.3 libvsomeip3-e2e.so.3.5.1: __middleware_someip_symlink_vsomeip3_e2e___build_minieye_toolchain_gcc__rule | ../../middleware/someip/src/create_symlinks.py ./libvsomeip3-e2e.so

build obj/middleware/someip/symlink_vsomeip3_e2e.stamp: stamp libvsomeip3-e2e.so.3 libvsomeip3-e2e.so.3.5.1
rule __middleware_someip_symlink_vsomeip3_sd___build_minieye_toolchain_gcc__rule
  command = python ../../middleware/someip/src/create_symlinks.py /home/<USER>/project/d4q/out/D4Q_debug libvsomeip3-sd.so.3.5.1
  description = ACTION //middleware/someip:symlink_vsomeip3_sd(//build/minieye/toolchain:gcc)
  restat = 1

build libvsomeip3-sd.so.3 libvsomeip3-sd.so.3.5.1: __middleware_someip_symlink_vsomeip3_sd___build_minieye_toolchain_gcc__rule | ../../middleware/someip/src/create_symlinks.py ./libvsomeip3-sd.so

build obj/middleware/someip/symlink_vsomeip3_sd.stamp: stamp libvsomeip3-sd.so.3 libvsomeip3-sd.so.3.5.1
subninja obj/middleware/someip/vsomeip3.ninja
subninja obj/middleware/someip/vsomeip3-cfg.ninja
subninja obj/middleware/someip/vsomeip3-e2e.ninja
subninja obj/middleware/someip/vsomeip3-sd.ninja
build obj/middleware/someip/vsomeip3_group.stamp: stamp ./libvsomeip3.so ./libvsomeip3-cfg.so ./libvsomeip3-e2e.so ./libvsomeip3-sd.so
build obj/middleware/system/core/filelog/filelog_group.stamp: stamp ./libfilelog.so
subninja obj/middleware/system/core/filelog/libfilelog.ninja
subninja obj/middleware/system/core/filelog/demo/demo_filelog.ninja
build obj/middleware/system/core/filelog/demo/filelog_demo.stamp: stamp ./demo_filelog
subninja obj/middleware/system/core/libcppbase/cppbase.ninja
subninja obj/middleware/system/core/libjsonUtil/jsonUtil.ninja
build obj/middleware/system/core/libjsonUtil/jsonUtil_group.stamp: stamp ./libjsonUtil.so bin/sample_json_test
subninja obj/middleware/system/core/libjsonUtil/sample_json_test.ninja
subninja obj/middleware/system/core/libmessage/message.ninja
subninja obj/middleware/system/core/libmessage/demo/sample_message.ninja
subninja obj/middleware/system/tools/filemonitor/file_monitor.ninja
build bin/archive-log.sh: copy ../../middleware/system/tools/log_global_save/archive-log.sh

build obj/middleware/system/tools/log_global_save/archive-log.stamp: stamp bin/archive-log.sh
build obj/middleware/system/tools/log_global_save/log_tools.stamp: stamp obj/middleware/system/tools/log_global_save/minieye-log.stamp obj/middleware/system/tools/log_global_save/archive-log.stamp
build bin/minieye-log: copy ../../middleware/system/tools/log_global_save/minieye-log

build obj/middleware/system/tools/log_global_save/minieye-log.stamp: stamp bin/minieye-log
subninja obj/middleware/system/tools/log_to_libflow/log_to_dds.ninja
subninja obj/middleware/system/tools/log_to_libflow/log_to_libflow.ninja
subninja obj/middleware/system/tools/scantree/scantree.ninja
build libs/libprotobuf_arm64_for_system_res_monitor.a: copy ../../middleware/system/tools/system_res_monitor/src/lib/libprotobuf_arm64_for_system_res_monitor.a

build obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp: stamp libs/libprotobuf_arm64_for_system_res_monitor.a
subninja obj/middleware/system/tools/system_res_monitor/system_res_monitor.ninja
build obj/middleware/tombstone/tombstone_group.stamp: stamp bin/backtrace_tool bin/tombstone bin/test ./libtombstone_client.so bin/sample_tombstone
subninja obj/middleware/tombstone/sample/sample_tombstone.ninja
subninja obj/middleware/tombstone/src/backtrace/backtrace_tombstone.ninja
subninja obj/middleware/tombstone/src/backtrace/demangle/demangle_tombstone.ninja
subninja obj/middleware/tombstone/src/base/base_tombstone.ninja
subninja obj/middleware/tombstone/src/debuggerd/test.ninja
subninja obj/middleware/tombstone/src/debuggerd/tombstone.ninja
subninja obj/middleware/tombstone/src/debuggerd/tombstone_client.ninja
subninja obj/middleware/tombstone/src/libcutils/libcutils_tombstone.ninja
subninja obj/middleware/tombstone/src/procinfo/procinfo_tombstone.ninja
subninja obj/middleware/tombstone/src/unwindstack/unwindstack_tombstone.ninja
subninja obj/middleware/tombstone/src/unwindstack/lzma/lzma_tombstone.ninja
subninja obj/middleware/tombstone/utils/backtrace_tool.ninja
subninja obj/middleware/upper_tester/upper_tester.ninja
build obj/middleware/upper_tester/upper_tester_group.stamp: stamp base/upper_tester/bin/upper_tester
build base/upper_tester/run.sh: copy ../../middleware/upper_tester/src/run.sh

build obj/middleware/upper_tester/ut_run.stamp: stamp base/upper_tester/run.sh
